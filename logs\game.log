 
2025-06-25 00:35:17 - game - INFO - [Log<PERSON>anager] 日志管理器已启动
2025-06-25 00:35:17 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 14 个技能。
2025-06-25 00:35:40 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 00:35:40 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 14 个技能。
2025-06-25 00:42:00 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 00:42:00 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 14 个技能。
2025-06-25 01:02:59 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 01:02:59 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 14 个技能。
2025-06-25 09:12:22 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 09:12:22 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 14 个技能。
2025-06-25 09:12:41 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 09:21:24 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 09:21:24 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:22:03 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 09:35:22 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 09:35:22 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:35:31 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:35:32 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:35:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:35:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:35:34 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:35:35 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:35:50 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 09:49:11 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 09:49:11 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:16 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:17 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:24 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:28 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:29 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:30 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:30 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:31 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:32 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:37 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:38 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 09:49:39 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 09:59:34 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 09:59:34 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:00:42 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 10:02:28 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 10:02:28 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:02:51 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 10:04:08 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 10:04:08 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:19:25 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 10:19:30 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 10:19:30 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:20:10 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 10:20:41 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 10:20:41 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:22:58 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 10:26:09 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 10:26:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:26:15 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:26:17 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:26:51 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 10:43:03 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 10:43:03 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 10:43:19 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 11:01:09 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 11:01:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 11:01:15 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 16:27:05 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 16:27:05 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 16:27:18 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-25 16:31:09 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 16:31:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 21:53:14 - game - INFO - [LogManager] 日志管理器已启动
2025-06-25 21:53:14 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-25 21:53:24 - game.models.skill_loader - INFO - 技能数据已保存到: e:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-26 11:35:15 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 11:35:15 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 11:35:20 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 11:35:22 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 11:35:24 - game.models.skill_loader - INFO - 技能数据已保存到: E:\BaiduNetdiskDownload\IDM\Demo\新建文件夹\game\data\skills.json
2025-06-26 17:59:36 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 17:59:36 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:00:06 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:00:06 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:02:05 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:02:05 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:04:07 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:04:07 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:04:13 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:07:29 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:07:29 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:07:41 - game.models.skill_loader - INFO - 技能 魔法技能/skill_1 已更新。
2025-06-26 18:07:43 - game.models.skill_loader - INFO - 技能 火球术 从等级 1 升级到 2
2025-06-26 18:07:43 - game.models.skill_loader - INFO - 技能 魔法技能/skill_1 已更新。
2025-06-26 18:07:45 - game.models.skill_loader - INFO - 技能 火球术 从等级 2 升级到 3
2025-06-26 18:07:45 - game.models.skill_loader - INFO - 技能 魔法技能/skill_1 已更新。
2025-06-26 18:30:42 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:30:42 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:36:58 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:36:58 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:44:05 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:44:05 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 18:44:41 - game - WARNING - [ItemSlot] 图片文件不存在: equipment/武器/乌木剑.png
2025-06-26 18:49:57 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 18:49:57 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 19:04:29 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 19:04:29 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 19:14:37 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 19:14:37 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 19:20:51 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 19:20:51 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 19:25:24 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 19:25:24 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:11:33 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:11:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:23:31 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:23:31 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:24:06 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:24:06 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:28:03 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:28:03 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:34:09 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:34:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:38:12 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:38:12 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:43:03 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:43:03 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:50:21 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:50:21 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 20:50:42 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 20:50:42 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 21:21:34 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 21:21:34 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 21:25:37 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 21:25:37 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 21:51:36 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 21:51:36 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 22:03:12 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 22:03:12 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-26 22:09:59 - game - INFO - [LogManager] 日志管理器已启动
2025-06-26 22:09:59 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-27 07:11:42 - game - INFO - [LogManager] 日志管理器已启动
2025-06-27 07:11:42 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-27 11:50:02 - game - INFO - [LogManager] 日志管理器已启动
2025-06-27 11:50:02 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 07:51:18 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 07:51:18 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 07:56:59 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 07:56:59 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 07:57:19 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 07:57:19 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 10:17:06 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 10:17:06 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 14:35:07 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 14:35:07 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 14:35:58 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 14:35:58 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 15:10:17 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 15:10:17 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-28 15:50:16 - game - INFO - [LogManager] 日志管理器已启动
2025-06-28 15:50:16 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-29 16:55:09 - game - INFO - [LogManager] 日志管理器已启动
2025-06-29 16:55:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-30 14:56:40 - game - INFO - [LogManager] 日志管理器已启动
2025-06-30 14:56:40 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-30 15:18:12 - game - INFO - [LogManager] 日志管理器已启动
2025-06-30 15:18:12 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-30 18:57:34 - game - INFO - [LogManager] 日志管理器已启动
2025-06-30 18:57:34 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-30 19:06:26 - game - INFO - [LogManager] 日志管理器已启动
2025-06-30 19:06:26 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-30 19:34:42 - game - INFO - [LogManager] 日志管理器已启动
2025-06-30 19:34:42 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-06-30 21:32:55 - game - INFO - [LogManager] 日志管理器已启动
2025-06-30 21:32:55 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-01 06:54:03 - game - INFO - [LogManager] 日志管理器已启动
2025-07-01 06:54:03 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-01 07:04:47 - game - INFO - [LogManager] 日志管理器已启动
2025-07-01 07:04:47 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-01 07:05:00 - game.models.skill_loader - INFO - 技能 魔法技能/skill_2 已更新。
2025-07-01 07:05:05 - game.models.skill_loader - INFO - 技能 魔法技能/skill_3 已更新。
2025-07-02 08:03:41 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 08:03:41 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 08:25:33 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 08:25:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 08:42:49 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 08:42:49 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 09:16:45 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 09:16:45 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 09:24:15 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 09:24:15 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 09:36:15 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 09:36:15 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 09:48:06 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 09:48:06 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 12:07:54 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 12:07:54 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 12:40:43 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 12:40:43 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 12:51:41 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 12:51:41 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 12:56:57 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 12:56:57 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 13:07:03 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 13:07:03 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 15:49:34 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 15:49:34 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 16:13:02 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 16:13:02 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 16:13:49 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 16:13:49 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 16:22:51 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 16:22:51 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 19:58:49 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 19:58:49 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 19:58:49 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 状态初始化失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:49 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:50 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:51 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:52 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:52 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:52 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:52 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:52 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:52 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 19:58:52 - game.ui.map_panel - ERROR - 渲染标签页失败: 'MapManager' object has no attribute 'get_categories'
2025-07-02 20:04:38 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:04:38 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:04:38 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:04:38 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:04:38 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:38 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:39 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:04:40 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:06:41 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:06:41 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:06:41 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:06:41 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:41 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:06:42 - game.ui.map_panel - ERROR - 渲染地图网格失败: 'MapGridState' object has no attribute 'add_cell'
2025-07-02 20:11:40 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:11:40 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:11:40 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:11:40 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:11:40 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:11:40 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:11:43 - game.ui.map_panel - INFO - 选择了地图: 沃玛森林 (ID: map_002)
2025-07-02 20:11:43 - game.ui.map_panel - INFO - 成功切换到地图: 沃玛森林
2025-07-02 20:11:44 - game.ui.map_panel - INFO - 选择了地图: 比奇省 (ID: map_001)
2025-07-02 20:11:44 - game.ui.map_panel - INFO - 成功切换到地图: 比奇省
2025-07-02 20:11:46 - game.ui.map_panel - INFO - 选择了地图: 比奇矿区 (ID: map_004)
2025-07-02 20:11:46 - game.ui.map_panel - INFO - 成功切换到地图: 比奇矿区
2025-07-02 20:11:46 - game.ui.map_panel - INFO - 选择了地图: 毒蛇山谷 (ID: map_005)
2025-07-02 20:11:46 - game.ui.map_panel - INFO - 成功切换到地图: 毒蛇山谷
2025-07-02 20:11:47 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:48 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:49 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:49 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:49 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:50 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:50 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:50 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:50 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:51 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:51 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:52 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:11:53 - game.ui.map_panel - INFO - 切换到标签页: 副本
2025-07-02 20:11:55 - game.ui.map_panel - INFO - 切换到标签页: 玛法
2025-07-02 20:11:58 - game.ui.map_panel - INFO - 切换到标签页: 副本
2025-07-02 20:11:59 - game.ui.map_panel - INFO - 切换到标签页: 玛法
2025-07-02 20:13:58 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:13:58 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:13:58 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:13:58 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:13:58 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:13:58 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:16:25 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:16:25 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:16:25 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:16:25 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:16:25 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:16:25 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:16:35 - game.ui.map_panel - INFO - 选择了地图: 比奇省 (ID: map_001)
2025-07-02 20:16:35 - game.ui.map_panel - INFO - 成功切换到地图: 比奇省
2025-07-02 20:16:35 - game.ui.map_panel - INFO - 选择了地图: 沃玛森林 (ID: map_002)
2025-07-02 20:16:35 - game.ui.map_panel - INFO - 成功切换到地图: 沃玛森林
2025-07-02 20:20:18 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:20:18 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:20:18 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:20:18 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:20:18 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:20:18 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:20:20 - game.ui.map_panel - INFO - 选择了地图: 骷髅洞 (ID: map_003)
2025-07-02 20:20:20 - game.ui.map_panel - INFO - 成功切换到地图: 骷髅洞
2025-07-02 20:20:53 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:20:53 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:20:53 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:20:53 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:20:53 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:20:53 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:39:57 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:39:57 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:39:57 - game.ui.map_panel - INFO - 所有管理器初始化成功
2025-07-02 20:39:57 - game.ui.map_panel - ERROR - UI组件初始化失败: 'pygame.surface.Surface' object has no attribute 'x'
2025-07-02 20:39:57 - game.ui.map_panel - INFO - 状态初始化成功
2025-07-02 20:39:57 - game.ui.map_panel - INFO - MapPanel 初始化完成
2025-07-02 20:39:59 - game.ui.map_panel - INFO - 选择了地图: 沃玛森林 (ID: map_002)
2025-07-02 20:39:59 - game.ui.map_panel - INFO - 成功切换到地图: 沃玛森林
2025-07-02 20:40:02 - game.ui.map_panel - INFO - 选择了地图: 比奇省 (ID: map_001)
2025-07-02 20:40:02 - game.ui.map_panel - INFO - 成功切换到地图: 比奇省
2025-07-02 20:40:02 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:40:04 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:40:05 - game.ui.map_panel - INFO - 翻到下一页
2025-07-02 20:47:05 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:47:05 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:48:48 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:48:48 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:49:21 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:49:21 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:50:26 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:50:26 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 20:50:39 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 20:50:39 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 21:15:27 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 21:15:27 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 21:37:38 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 21:37:38 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 22:17:18 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 22:17:18 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 23:13:45 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 23:13:45 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-02 23:19:22 - game - INFO - [LogManager] 日志管理器已启动
2025-07-02 23:19:22 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 08:50:20 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 08:50:20 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 08:52:07 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 08:52:07 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 08:53:09 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 08:53:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 08:55:52 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 08:55:52 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 08:56:49 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 08:56:49 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 08:57:26 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 08:57:26 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 09:00:14 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 09:00:14 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 09:01:01 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 09:01:01 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 09:24:17 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 09:24:17 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 09:41:22 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 09:41:22 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 10:07:59 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 10:07:59 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 10:49:26 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 10:49:26 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 10:52:36 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 10:52:36 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 10:53:54 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 10:53:54 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 11:24:33 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 11:24:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 11:50:49 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 11:50:49 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 11:53:51 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 11:53:51 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 12:25:44 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 12:25:44 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 13:55:04 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 13:55:04 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 14:00:59 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 14:00:59 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 14:04:10 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 14:04:10 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 14:28:05 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 14:28:05 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 15:01:04 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 15:01:04 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 15:01:11 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 15:01:11 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 15:10:37 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 15:10:37 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 15:35:32 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 15:35:32 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 15:49:23 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 15:49:23 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 15:54:36 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 15:54:36 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 16:23:33 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 16:23:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 16:28:33 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 16:28:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 18:31:48 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 18:31:48 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 18:38:16 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 18:38:16 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 18:43:12 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 18:43:12 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 18:51:05 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 18:51:05 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 18:56:36 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 18:56:36 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 20:12:50 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 20:12:50 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 20:39:28 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 20:39:28 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 21:17:06 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 21:17:06 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 21:21:37 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 21:21:37 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 21:34:03 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 21:34:03 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 21:35:17 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 21:35:17 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 21:43:25 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 21:43:25 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 21:48:12 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 21:48:12 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-03 22:29:38 - game - INFO - [LogManager] 日志管理器已启动
2025-07-03 22:29:38 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 07:08:51 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 07:08:51 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 07:15:09 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 07:15:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 07:54:40 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 07:54:40 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 08:57:02 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 08:57:02 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 11:57:20 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 11:57:20 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 12:08:11 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 12:08:11 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 12:09:23 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 12:09:23 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 12:13:50 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 12:13:50 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 12:50:45 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 12:50:45 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 13:33:41 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 13:33:41 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 13:35:41 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 13:35:41 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 17:11:45 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 17:11:45 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 17:30:48 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 17:30:48 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 18:58:31 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 18:58:31 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:01:33 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:01:33 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:19:41 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:19:41 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:22:35 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:22:35 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:29:59 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:29:59 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:31:17 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:31:17 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:49:16 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:49:16 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:51:42 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:51:42 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:53:46 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:53:46 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 19:58:05 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 19:58:05 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 21:47:58 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 21:47:58 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 21:57:02 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 21:57:02 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:07:51 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:07:51 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:09:49 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:09:49 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:13:02 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:13:02 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:16:39 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:16:39 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:21:08 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:21:08 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:22:13 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:22:13 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:23:57 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:23:57 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:28:22 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:28:22 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 22:58:14 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 22:58:14 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:04:04 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:04:04 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:07:13 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:07:13 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:09:42 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:09:42 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:13:09 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:13:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:16:36 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:16:36 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:18:23 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:18:23 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:19:24 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:19:24 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-04 23:19:56 - game - INFO - [LogManager] 日志管理器已启动
2025-07-04 23:19:56 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 07:04:50 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 07:04:50 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 07:14:03 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 07:14:03 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 07:14:23 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 07:14:23 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 11:29:32 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 11:29:32 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 11:45:54 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 11:45:54 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 11:49:46 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 11:49:46 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 11:52:10 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 11:52:10 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 11:52:57 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 11:52:57 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:02:54 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:02:54 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:03:25 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:03:25 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:22:00 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:22:00 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:23:02 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:23:02 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:30:24 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:30:24 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:34:08 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:34:08 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:39:09 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:39:09 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:42:44 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:42:44 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
2025-07-05 12:44:13 - game - INFO - [LogManager] 日志管理器已启动
2025-07-05 12:44:13 - game.models.skill_loader - INFO - 技能数据加载成功: 4 个职业，共 22 个技能。
