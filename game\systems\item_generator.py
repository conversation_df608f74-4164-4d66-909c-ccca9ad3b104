"""
物品生成器系统
负责生成物品实例和管理怪物掉落
"""
import json
import random
import os
from typing import Dict, List, Optional, Any
from game.core.resource_manager import get_game_data_path


class Item:
    """物品类"""
    def __init__(self, item_data: Dict[str, Any], quantity: int = 1):
        """初始化物品"""
        # 🔧 修复：先设置基础属性，再生成依赖这些属性的ID
        self.name = item_data.get('name', '')
        self.type = item_data.get('type', 'consumable')
        self.category = item_data.get('category', '')
        
        # 现在可以安全生成ID了
        self.id = item_data.get('id', self._generate_item_id())
        
        self.description = item_data.get('description', '')
        self.icon_path = item_data.get('icon_path', '')
        
        # 数量属性
        self.quantity = quantity
        self.stackable = item_data.get('stackable', True)
        self.max_stack = item_data.get('max_stack', 99)
        
        # 价值属性
        self.sell_price = item_data.get('sell_price', 1)
        
        # 装备相关属性
        self.attributes = item_data.get('attributes', {})
        self.level_requirement = item_data.get('level_requirement', 1)
        self.required_class = item_data.get('required_class', [])
        self.slot = item_data.get('slot', '')
        self.durability = item_data.get('durability', 100)
        self.max_durability = item_data.get('max_durability', 100)
        self.rarity = item_data.get('rarity', 'common')
        
        # 特殊属性
        self.special = item_data.get('special', '')
        self.requirements = item_data.get('requirements', {})
        
        # 装备特有属性
        self.gender = item_data.get("gender", None)
        
        # 消耗品特有属性
        self.effects = item_data.get("effects", {})
        
        # 技能书特有属性
        self.skill_level = item_data.get("skill_level", 1)
    
    def _generate_item_id(self) -> str:
        """生成唯一物品ID"""
        import time
        return f"{self.name}_{int(time.time() * 1000)}"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将物品对象转换为字典格式
        
        返回:
            Dict[str, Any]: 物品的字典表示
        """
        return {
            'id': self.id,
            'name': self.name,
            'type': self.type,
            'category': self.category,
            'icon_path': self.icon_path,
            'description': self.description,
            'quantity': self.quantity,
            'stackable': self.stackable,
            'max_stack': self.max_stack,
            'rarity': self.rarity,
            'sell_price': self.sell_price,
            'attributes': self.attributes,
            'level_requirement': self.level_requirement,
            'durability': self.durability,
            'max_durability': self.max_durability,
            'gender': self.gender,
            'effects': self.effects,
            'skill_level': getattr(self, 'skill_level', 1)
        }
    
    @classmethod
    def from_dict(cls, item_dict: Dict[str, Any]) -> "Item":
        """从字典创建物品实例"""
        item = cls(item_dict, item_dict.get("quantity", 1))
        item.id = item_dict.get("id", item._generate_item_id())
        return item


class ItemGenerator:
    """
    物品生成器类
    负责根据配置生成各类游戏物品
    """
    
    def __init__(self):
        self.items_config = {}
        self.drop_config = {}
        
        # 加载配置文件
        self.load_configs()
        
    def load_configs(self):
        """加载物品配置和掉落配置"""
        try:
            # 使用资源管理器获取正确的文件路径
            items_path = get_game_data_path("items_config.json")
            print(f"加载物品配置: {items_path}")
            
            if os.path.exists(items_path):
                with open(items_path, 'r', encoding='utf-8') as f:
                    self.items_config = json.load(f)
                print(f"物品配置加载完成: {len(self.items_config)} 个物品类型")
            
            # 加载装备配置
            equipment_path = get_game_data_path("equipmengt.json")
            print(f"加载装备配置: {equipment_path}")
            
            if os.path.exists(equipment_path):
                with open(equipment_path, 'r', encoding='utf-8') as f:
                    equipment_config = json.load(f)
                    
                # 将装备数据合并到items_config中
                equipment_db = equipment_config.get("equipment_db", {})
                for category, items in equipment_db.items():
                    if isinstance(items, list):
                        for item in items:
                            if isinstance(item, dict) and 'name' in item:
                                # 转换装备数据格式为标准物品格式
                                item_name = item['name']
                                equipment_slot = self._get_equipment_slot(category)
                                
                                # 提取装备属性 - 修复属性处理逻辑
                                attributes = {}
                                exclude_keys = ['name', 'type', 'level', 'tier', 'requirements']
                                
                                for k, v in item.items():
                                    if k not in exclude_keys:
                                        # 确保属性是正确的数据类型
                                        if isinstance(v, list) and len(v) == 2:
                                            # 范围属性，确保是数字
                                            try:
                                                attributes[k] = [int(v[0]), int(v[1])]
                                            except (ValueError, TypeError):
                                                attributes[k] = v
                                        elif isinstance(v, (int, float)):
                                            attributes[k] = v
                                        else:
                                            attributes[k] = v
                                
                                # 添加品质信息
                                tier = item.get('tier', '普通')
                                if tier == '普通':
                                    quality = 'common'
                                elif tier == '精良':
                                    quality = 'rare'
                                elif tier == '稀有':
                                    quality = 'epic'
                                elif tier == '传说':
                                    quality = 'legendary'
                                else:
                                    quality = 'common'
                                
                                # 计算基础价格
                                base_price = item.get('level', 1) * 10
                                if quality == 'rare':
                                    base_price *= 2
                                elif quality == 'epic':
                                    base_price *= 5
                                elif quality == 'legendary':
                                    base_price *= 10
                                
                                self.items_config[item_name] = {
                                    'name': item_name,
                                    'type': 'equipment',
                                    'category': category,
                                    'level_requirement': item.get('level', 1),
                                    'rarity': quality,
                                    'attributes': attributes,
                                    'requirements': item.get('requirements', {}),
                                    'stackable': False,
                                    'max_stack': 1,
                                    'sell_price': base_price,
                                    'description': f"{category} - {tier}品质",
                                    'slot': equipment_slot,
                                    'icon_path': f"game/assets/images/equipment/{category}/{item_name}.png",
                                    'durability': 100,
                                    'max_durability': 100
                                }
                print(f"装备配置加载完成: {len(equipment_db)} 个装备类别")
            
            # 加载掉落配置
            drop_path = get_game_data_path("drop_rates.json")
            print(f"加载掉落配置: {drop_path}")
            
            if os.path.exists(drop_path):
                with open(drop_path, 'r', encoding='utf-8') as f:
                    self.drop_config = json.load(f)
                print(f"掉落配置加载完成")
            
        except Exception as e:
            print(f"加载配置文件时出错: {e}")
            # 使用默认配置
            self._load_default_config()
            
    def _load_default_config(self):
        """加载默认配置"""
        self.items_config = {
            "消耗品": {
                "金创药(小量)": {"type": "治疗药", "effect": {"hp": 50}, "value": 10},
                "魔法药(小量)": {"type": "魔法药", "effect": {"mp": 30}, "value": 10}
            }
        }
        
        self.drop_config = {
            "默认": {
                "gold_rate": 0.8,
                "item_rate": 0.3,
                "equipment_rate": 0.1
            }
        }
    
    def _count_all_items(self) -> int:
        """统计所有物品数量"""
        return len(self.items_config) if isinstance(self.items_config, dict) else 0
    
    def get_item_data(self, item_name: str) -> Optional[Dict[str, Any]]:
        """获取物品数据"""
        return self.items_config.get(item_name)
    
    def create_item(self, item_name: str, quantity: int = 1) -> Optional[Item]:
        """创建物品实例"""
        item_data = self.get_item_data(item_name)
        if item_data:
            # 如果是装备，随机生成品质并调整属性
            if item_data.get('type') == 'equipment':
                return self._create_equipment_with_quality(item_data, quantity)
            else:
                return Item(item_data, quantity)
        else:
            print(f"❌ 未找到物品数据: '{item_name}'")
            return None
    
    def _create_equipment_with_quality(self, base_item_data: Dict[str, Any], quantity: int = 1) -> Item:
        """根据品质创建装备，动态调整属性"""
        # 复制基础数据
        item_data = base_item_data.copy()
        
        # 随机生成品质
        quality = self._generate_random_quality()
        
        # 获取品质固定加成点数
        quality_bonuses = {
            'common': {'bonus_points': 0, 'name': '普通'},
            'uncommon': {'bonus_points': 1, 'name': '精良'},
            'rare': {'bonus_points': 2, 'name': '稀有'},
            'epic': {'bonus_points': 3, 'name': '史诗'},
            'legendary': {'bonus_points': 4, 'name': '传说'}
        }
        
        quality_info = quality_bonuses.get(quality, quality_bonuses['common'])
        bonus_points = quality_info['bonus_points']
        quality_name = quality_info['name']
        
        # 调整装备属性
        enhanced_attributes = {}
        base_attributes = item_data.get('attributes', {})
        
        for attr_name, attr_value in base_attributes.items():
            if isinstance(attr_value, list) and len(attr_value) == 2:
                # 处理范围属性 [min, max]
                min_val, max_val = attr_value
                # 应用品质加成：基础属性 + 固定点数
                enhanced_min = min_val + bonus_points
                enhanced_max = max_val + bonus_points
                enhanced_attributes[attr_name] = [enhanced_min, enhanced_max]
            elif isinstance(attr_value, (int, float)):
                # 处理单值属性
                enhanced_value = attr_value + bonus_points
                enhanced_attributes[attr_name] = enhanced_value
            else:
                # 保持原值
                enhanced_attributes[attr_name] = attr_value
        
        # 为高品质装备添加额外属性
        if quality in ['epic', 'legendary']:
            enhanced_attributes = self._add_bonus_attributes(enhanced_attributes, quality)
        
        # 更新物品数据
        item_data['attributes'] = enhanced_attributes
        item_data['rarity'] = quality
        item_data['description'] = f"{item_data.get('category', '装备')} - {quality_name}品质"
        
        # 根据品质调整价格（固定加成）
        base_price = item_data.get('sell_price', 10)
        price_bonus = bonus_points * 5  # 每个品质点增加5金币价值
        item_data['sell_price'] = base_price + price_bonus
        
        # 根据品质调整耐久度（固定加成）
        base_durability = 100
        durability_bonus = bonus_points * 20  # 每个品质点增加20耐久度
        max_durability = base_durability + durability_bonus
        item_data['durability'] = max_durability
        item_data['max_durability'] = max_durability
        
        return Item(item_data, quantity)
    
    def _generate_random_quality(self) -> str:
        """随机生成装备品质"""
        # 品质掉落概率
        quality_chances = [
            ('common', 60),     # 60% 普通
            ('uncommon', 25),   # 25% 精良
            ('rare', 10),       # 10% 稀有
            ('epic', 4),        # 4% 史诗
            ('legendary', 1)    # 1% 传说
        ]
        
        total_weight = sum(weight for _, weight in quality_chances)
        random_value = random.randint(1, total_weight)
        
        current_weight = 0
        for quality, weight in quality_chances:
            current_weight += weight
            if random_value <= current_weight:
                return quality
        
        return 'common'  # 默认返回普通
    
    def _add_bonus_attributes(self, attributes: Dict[str, Any], quality: str) -> Dict[str, Any]:
        """为高品质装备添加额外属性"""
        enhanced_attrs = attributes.copy()
        
        # 可能的额外属性
        bonus_attributes = [
            'luck',           # 幸运
            'attack_speed',   # 攻击速度
            'accuracy',       # 准确
            'agility',        # 敏捷
            'magic_avoidance' # 魔法躲避
        ]
        
        # 史诗品质可能获得1个额外属性
        if quality == 'epic' and random.random() < 0.5:
            bonus_attr = random.choice(bonus_attributes)
            if bonus_attr not in enhanced_attrs:
                enhanced_attrs[bonus_attr] = random.randint(1, 3)
        
        # 传说品质可能获得1-2个额外属性
        elif quality == 'legendary':
            num_bonus = random.randint(1, 2)
            selected_bonus = random.sample(bonus_attributes, min(num_bonus, len(bonus_attributes)))
            for bonus_attr in selected_bonus:
                if bonus_attr not in enhanced_attrs:
                    enhanced_attrs[bonus_attr] = random.randint(2, 5)
        
        return enhanced_attrs
    
    def generate_random_item(self, min_level: int = 1, max_level: int = 10, item_type: str = None) -> Optional[Item]:
        """生成随机物品"""
        suitable_items = []
        
        for item_name, item_data in self.items_config.items():
            # 检查等级要求
            item_level = item_data.get("level_requirement", 1)
            if min_level <= item_level <= max_level:
                # 检查物品类型
                if item_type is None or item_data.get("type") == item_type:
                    suitable_items.append(item_data)
        
        if suitable_items:
            selected_item = random.choice(suitable_items)
            return Item(selected_item)
        return None
    
    def get_monster_drops(self, monster_name: str, player_level: int = 1) -> List[Item]:
        """获取怪物掉落物品 - 仅使用配置文件中的掉落"""
        dropped_items = []
        
        # 从掉落率配置中获取怪物掉落
        monster_drops = self.drop_config.get("drop_rates", {}).get("monsters", {}).get(monster_name, {})
        drops = monster_drops.get("drops", [])
        
        for drop in drops:
            item_name = drop.get("item", "")
            drop_rate = drop.get("rate", 0.0)
            
            # 根据掉落率判断是否掉落
            if random.random() <= drop_rate:
                item = self.create_item(item_name)
                if item:
                    # 对于可堆叠物品，随机生成数量
                    if item.stackable:
                        max_drop = min(item.max_stack, 5)  # 最多掉落5个
                        item.quantity = random.randint(1, max_drop)
                    dropped_items.append(item)
        
        
        return dropped_items
    
    
    

    
    def get_items_by_type(self, item_type: str) -> List[str]:
        """获取指定类型的所有物品名称"""
        items = []
        for item_name, item_data in self.items_config.items():
            if item_data.get("type") == item_type:
                items.append(item_name)
        return items
    
    def get_items_by_category(self, category: str) -> List[str]:
        """获取指定分类的所有物品名称"""
        return list(self.items_config.get(category, {}).keys())

    def _get_equipment_slot(self, category: str) -> str:
        """根据装备类别获取装备槽位"""
        slot_mapping = {
            "武器": "武器",
            "防具": "胸甲", 
            "头盔": "头盔",
            "手镯": "护符",
            "戒指": "戒指",
            "项链": "项链",
            "勋章": "勋章"
        }
        return slot_mapping.get(category, "其他")


# 全局物品生成器实例
item_generator = ItemGenerator()

def reload_item_generator():
    """重新加载物品生成器配置"""
    global item_generator
    item_generator = ItemGenerator()
    print("物品生成器配置已重新加载") 