# -*- coding: utf-8 -*-
"""
重构后的玩家角色类
使用管理器模式分离职责，提高代码可维护性
"""

import math
import json
import os
from typing import Dict, List, Tuple, Optional, Any

from game.models.character_stats import get_character_stats, get_class_info, get_available_classes
from game.managers import (
    InventoryManager, EquipmentManager, SkillManager, 
    QuestManager, SaveLoadManager
)


class CharacterStats:
    """
    角色属性类
    用于替代原来的字典形式的stats属性
    """
    
    def __init__(self, character_class: str, level: int):
        """
        初始化角色属性
        
        参数:
            character_class: 职业名称
            level: 角色等级
        """
        self.character_class = character_class
        self.level = level
        self._base_stats = get_character_stats(character_class, level)
        self._equipment_bonus = {}
        self._buff_bonus = {}
        # 🔧 新增：技能加成
        self._skill_bonus = {}
    
    def update_level(self, new_level: int):
        """
        更新等级并重新计算基础属性
        
        参数:
            new_level: 新等级
        """
        self.level = new_level
        self._base_stats = get_character_stats(self.character_class, new_level)
    
    def set_equipment_bonus(self, equipment_stats: Dict[str, Any]):
        """
        设置装备加成
        
        参数:
            equipment_stats: 装备属性加成
        """
        if equipment_stats:
            self._equipment_bonus = equipment_stats.copy()
        else:
            self._equipment_bonus = {}

    def set_skill_bonus(self, skill_stats: Dict[str, Any]):
        """
        设置技能加成

        参数:
            skill_stats: 技能属性加成
        """
        if skill_stats:
            self._skill_bonus = skill_stats.copy()
        else:
            self._skill_bonus = {}

    def set_buff_bonus(self, buff_stats: Dict[str, Any]):
        """
        设置增益效果加成
        
        参数:
            buff_stats: 增益属性加成
        """
        self._buff_bonus = buff_stats.copy()
    
    def get_stat(self, stat_name: str) -> Any:
        """
        获取指定属性值（包含所有加成）
        
        参数:
            stat_name: 属性名称
            
        返回:
            属性值
        """
        base_value = self._base_stats.get(stat_name, 0)
        equipment_bonus = self._equipment_bonus.get(stat_name, 0)
        buff_bonus = self._buff_bonus.get(stat_name, 0)
        # 🔧 新增：技能加成
        skill_bonus = self._skill_bonus.get(stat_name, 0)

        return base_value + equipment_bonus + buff_bonus + skill_bonus
    
    def get_max_hp(self) -> int:
        """
        获取最大生命值
        
        返回:
            最大生命值
        """
        return self.get_stat('生命值')
    
    def get_max_mp(self) -> int:
        """
        获取最大魔法值
        
        返回:
            最大魔法值
        """
        return self.get_stat('魔法值')
    
    @property
    def attack_min(self) -> int:
        """获取最小物理攻击力"""
        return self.get_stat('攻击力')
    
    @property
    def attack_max(self) -> int:
        """获取最大物理攻击力"""
        return self.get_stat('攻击力')
    
    @property
    def magic_min(self) -> int:
        """获取最小魔法攻击力"""
        return self.get_stat('魔法力')
    
    @property
    def magic_max(self) -> int:
        """获取最大魔法攻击力"""
        return self.get_stat('魔法力')
    
    @property
    def tao_min(self) -> int:
        """获取最小道术攻击力"""
        return self.get_stat('道术力')
    
    @property
    def tao_max(self) -> int:
        """获取最大道术攻击力"""
        return self.get_stat('道术力')
    
    @property
    def defense_min(self) -> int:
        """获取最小防御力"""
        return self.get_stat('防御力')
    
    @property
    def defense_max(self) -> int:
        """获取最大防御力"""
        return self.get_stat('防御力')
    
    @property
    def magic_defense(self) -> int:
        """获取魔法防御力"""
        return self.get_stat('魔法防御')
    
    def get_accuracy(self) -> int:
        """获取准确值"""
        return self.get_stat('准确')
    
    def get_agility(self) -> int:
        """获取敏捷值"""
        return self.get_stat('敏捷')
    
    def get_total_luck(self) -> int:
        """获取总幸运值"""
        return min(9, max(0, self.get_stat('幸运')))
    
    def get_total_curse(self) -> int:
        """获取总诅咒值"""
        return min(9, max(0, self.get_stat('诅咒')))
    
    @property
    def critical_rate(self) -> float:
        """获取暴击率"""
        return self.get_stat('暴击率') if self.get_stat('暴击率') else 0.05
    
    @property
    def critical_damage(self) -> float:
        """获取暴击伤害倍率"""
        return self.get_stat('暴击伤害') if self.get_stat('暴击伤害') else 1.5
    
    @property
    def attack_speed(self) -> float:
        """获取攻击速度"""
        return self.get_stat('攻速') if self.get_stat('攻速') else 1.0
    
    @property
    def move_speed(self) -> float:
        """获取移动速度"""
        return self.get_stat('移动速度') if self.get_stat('移动速度') else 1.0
    
    def get_all_stats(self) -> Dict[str, Any]:
        """
        获取所有属性
        
        返回:
            属性字典
        """
        all_stats = {}
        
        # 合并所有属性
        all_stat_names = set()
        all_stat_names.update(self._base_stats.keys())
        all_stat_names.update(self._equipment_bonus.keys())
        all_stat_names.update(self._buff_bonus.keys())
        # 🔧 新增：包含技能加成
        all_stat_names.update(self._skill_bonus.keys())
        
        for stat_name in all_stat_names:
            all_stats[stat_name] = self.get_stat(stat_name)
        
        return all_stats


class Player:
    """
    重构后的玩家角色类
    使用管理器模式分离职责
    """
    
    def __init__(self, name="勇者", character_class="战士", level=1, gender="男", data=None):
        """
        初始化玩家角色
        
        参数:
            name: 角色名称
            character_class: 职业名称
            level: 初始等级
            gender: 性别（男/女）
            data: 预设的角色数据，用于从存档加载
        """
        # 如果提供了数据，从数据中加载属性
        if data:
            self._load_from_data(data)
            return
        
        # 基本信息
        self.name = name
        self.character_class = character_class
        self.gender = gender
        self.level = level
        self.exp = 0
        self.exp_to_next_level = self._calculate_exp_to_level(level + 1)
        self.max_exp = self.exp_to_next_level  # 添加max_exp属性
        
        # 创建角色属性对象
        self.stats = CharacterStats(character_class, level)
        
        # 资源
        self.gold = 0
        self.silver = 0
        self.yuanbao = 0
        self.currencies = {
            'gold': 0,
            'silver': 0,
            'yuanbao': 0,
            'coin': 0,
            'points': 0  # 新增积分系统
        }
        
        # 签到系统数据
        self.daily_checkin = {
            'last_checkin_date': '',
            'consecutive_days': 0,
            'total_checkin_days': 0,
            'monthly_checkin_count': 0,
            'checkin_rewards_claimed': []
        }
        
        # VIP等级
        self.vip_level = 0
        self.vip_exp = 0
        
        # 击杀统计 - 新增功能用于地图解锁
        self.kill_statistics = {
            'total_monsters': 0,      # 总怪物击杀数
            'total_bosses': 0,        # 总Boss击杀数
            'map_kills': {},          # 每个地图的击杀统计 {'地图名': {'monsters': 数量, 'bosses': 数量}}
            'monster_types': {},      # 每种怪物类型的击杀统计 {'怪物名': 数量}
        }
        
        # 地图解锁进度
        self.map_unlock_progress = {}  # 地图解锁进度 {'地图名': {'monsters_needed': 数量, 'bosses_needed': 数量, 'unlocked': bool}}
        
        # 创建管理器
        self.inventory_manager = InventoryManager()
        self.equipment_manager = EquipmentManager()
        self.skill_manager = SkillManager()
        self.quest_manager = QuestManager()
        self.save_load_manager = SaveLoadManager()
        
        # 从属性对象获取基础属性（在管理器创建后）
        self._load_stats_from_character_stats()
        
        # 装备初始装备
        self.equipment_manager.equip_starter_equipment(self)
        
        # 重新计算属性（包含装备加成）
        self._load_stats_from_character_stats()
        
        # 加载职业技能
        self._load_class_skills()
        
        # 战斗状态
        self.is_alive = True
        self.in_battle = False
        self.target = None
        self.last_attack_time = 0
        
        # 寻怪状态
        self.is_hunting = False
        self.auto_hunt_enabled = False
        
        # 位置信息
        self.position = (0, 0)
        self.current_map = "新手村"
        
        # 确保基本属性已初始化（兼容存档系统）
        if not hasattr(self, 'gold'):
            self.gold = self.currencies.get('gold', 0)
        if not hasattr(self, 'silver'):
            self.silver = self.currencies.get('silver', 0)
        if not hasattr(self, 'yuanbao'):
            self.yuanbao = self.currencies.get('yuanbao', 0)
        
        # 战力计算
        self.battle_power = self._calculate_battle_power()
        self.combat_value = self.battle_power
        
        # 当前生命值和魔法值
        self.current_hp = self.hp
        self.current_mp = self.mp
        
        # 兼容性设置：让Player类自身的stats属性指向self，以便战斗系统能正确访问所有属性
        # 这样 player.stats.get_accuracy() 等方法都能正常工作
    
    def _load_from_data(self, data):
        """
        从数据字典中加载玩家数据
        
        参数:
            data: 数据字典
        """
        # 使用存档管理器加载数据
        self.save_load_manager = SaveLoadManager()
        
        # 先设置基本信息
        basic_info = data.get('basic_info', {})
        self.name = basic_info.get('name', '勇者')
        self.character_class = basic_info.get('character_class', '战士')
        self.gender = basic_info.get('gender', '男')
        self.level = basic_info.get('level', 1)
        self.exp = basic_info.get('exp', 0)
        self.exp_to_next_level = basic_info.get('exp_to_next_level', self._calculate_exp_to_level(self.level + 1))
        self.max_exp = self.exp_to_next_level  # 添加max_exp属性
        
        # 创建角色属性对象
        self.stats = CharacterStats(self.character_class, self.level)
        
        # 创建管理器
        self.inventory_manager = InventoryManager()
        self.equipment_manager = EquipmentManager()
        self.skill_manager = SkillManager()
        self.quest_manager = QuestManager()
        
        # 先初始化默认属性
        self.gold = 0
        self.silver = 0
        self.yuanbao = 0
        self.is_alive = True
        self.in_battle = False
        self.is_hunting = False
        self.auto_hunt_enabled = False
        self.position = (0, 0)
        self.current_map = "新手村"
        
        # 初始化VIP和货币系统
        self.vip_level = 0
        self.vip_exp = 0
        self.currencies = {
            'gold': 0,
            'silver': 0,
            'yuanbao': 0,
            'coin': 0
        }
        
        # 初始化击杀统计和地图解锁进度
        self.kill_statistics = {
            'total_monsters': 0,
            'total_bosses': 0,
            'map_kills': {},
            'monster_types': {}
        }
        self.map_unlock_progress = {}
        
        # 初始化基础属性（为apply_player_data准备）
        self.hp = 100
        self.mp = 50
        self.max_hp = 100
        self.max_mp = 50
        self.current_hp = 100
        self.current_mp = 50
        
        # 先应用存档数据（包括装备数据）
        self.save_load_manager.apply_player_data(self, data)
        
        # 然后再计算属性（这时装备数据已经加载）
        self._load_stats_from_character_stats()
        
        # 重新计算战力
        self.battle_power = self._calculate_battle_power()
        self.combat_value = self.battle_power
    
    def _load_stats_from_character_stats(self):
        """
        从CharacterStats对象加载属性到Player对象
        """
        # 更新装备加成
        equipment_stats = self.equipment_manager.get_equipment_stats()
        self.stats.set_equipment_bonus(equipment_stats)

        # 🔧 新增：获取被动技能加成
        skill_bonuses = self.skill_manager.get_passive_skill_bonuses()
        self.stats.set_skill_bonus(skill_bonuses)

        # 获取所有属性（包含装备加成和技能加成）
        all_stats = self.stats.get_all_stats()

        # 获取基础属性（不含装备加成）
        base_stats = self.stats._base_stats
        
        # 设置基础属性（不含装备加成）和最大值（含装备加成）
        self.base_hp = base_stats.get('生命值', 100)  # 基础生命值
        self.base_mp = base_stats.get('魔法值', 50)   # 基础魔法值
        self.hp = all_stats.get('生命值', 100)        # 总生命值（基础+装备）
        self.mp = all_stats.get('魔法值', 50)         # 总魔法值（基础+装备）
        self.max_hp = all_stats.get('生命值', 100)    # 最大生命值（基础+装备）
        self.max_mp = all_stats.get('魔法值', 50)     # 最大魔法值（基础+装备）
        
        # 设置攻击属性
        setattr(self, '攻击下限', all_stats.get('攻击下限', 1))
        setattr(self, '攻击上限', all_stats.get('攻击上限', 1))
        setattr(self, '防御下限', all_stats.get('防御下限', 0))
        setattr(self, '防御上限', all_stats.get('防御上限', 0))
        setattr(self, '魔法攻击下限', all_stats.get('魔法攻击下限', 0))
        setattr(self, '魔法攻击上限', all_stats.get('魔法攻击上限', 0))
        setattr(self, '道术攻击下限', all_stats.get('道术攻击下限', 0))
        setattr(self, '道术攻击上限', all_stats.get('道术攻击上限', 0))
        setattr(self, '魔抗', all_stats.get('魔抗', 0))
        setattr(self, '攻速', all_stats.get('攻速', 1.0))

        # 🔧 新增：设置技能相关属性
        setattr(self, '准确', all_stats.get('准确', 0))
        setattr(self, '敏捷', all_stats.get('敏捷', 0))
        setattr(self, '幸运', all_stats.get('幸运', 0))
        setattr(self, '暴击率', all_stats.get('暴击率', 0.05))  # 默认5%暴击率
        
        # 确保当前生命值和法力值不超过最大值
        if hasattr(self, 'current_hp'):
            self.current_hp = min(self.current_hp, self.max_hp)
        if hasattr(self, 'current_mp'):
            self.current_mp = min(self.current_mp, self.max_mp)
    
    def _load_class_skills(self):
        """
        加载职业技能
        """
        # 这里可以根据职业加载初始技能
        # 暂时留空，等待技能数据完善
        pass
    
    def _calculate_exp_to_level(self, target_level):
        """
        计算升级所需经验
        
        参数:
            target_level: 目标等级
            
        返回:
            所需经验值
        """
        if target_level <= 1:
            return 0
        return int(100 * (target_level - 1) * 1.2 ** (target_level - 1))
    
    def _calculate_battle_power(self):
        """
        计算战斗力
        
        返回:
            战斗力数值
        """
        # 基础属性权重
        hp_weight = 0.5
        mp_weight = 0.3
        attack_weight = 10
        defense_weight = 8
        magic_attack_weight = 10
        tao_attack_weight = 10
        magic_resist_weight = 5
        attack_speed_weight = 100
        
        # 计算战斗力
        battle_power = (
            self.hp * hp_weight +
            self.mp * mp_weight +
            (getattr(self, '攻击下限', 0) + getattr(self, '攻击上限', 0)) / 2 * attack_weight +
            (getattr(self, '防御下限', 0) + getattr(self, '防御上限', 0)) / 2 * defense_weight +
            (getattr(self, '魔法攻击下限', 0) + getattr(self, '魔法攻击上限', 0)) / 2 * magic_attack_weight +
            (getattr(self, '道术攻击下限', 0) + getattr(self, '道术攻击上限', 0)) / 2 * tao_attack_weight +
            getattr(self, '魔抗', 0) * magic_resist_weight +
            getattr(self, '攻速', 1.0) * attack_speed_weight
        )
        
        return int(battle_power)
    
    def level_up(self):
        """
        升级处理
        """
        if self.exp >= self.exp_to_next_level:
            self.level += 1
            self.exp -= self.exp_to_next_level
            self.exp_to_next_level = self._calculate_exp_to_level(self.level + 1)
            self.max_exp = self.exp_to_next_level  # 更新max_exp
            
            # 保存当前生命值和法力值的比例
            hp_ratio = 1.0
            mp_ratio = 1.0
            if hasattr(self, 'current_hp') and hasattr(self, 'max_hp') and self.max_hp > 0:
                hp_ratio = self.current_hp / self.max_hp
            if hasattr(self, 'current_mp') and hasattr(self, 'max_mp') and self.max_mp > 0:
                mp_ratio = self.current_mp / self.max_mp
            
            # 更新属性
            self.stats.update_level(self.level)
            self._load_stats_from_character_stats()
            
            # 根据比例恢复生命值和魔法值
            if hasattr(self, 'current_hp'):
                self.current_hp = int(self.max_hp * hp_ratio)
            else:
                self.current_hp = self.hp
                
            if hasattr(self, 'current_mp'):
                self.current_mp = int(self.max_mp * mp_ratio)
            else:
                self.current_mp = self.mp
            
            # 重新计算战力
            self.battle_power = self._calculate_battle_power()
            self.combat_value = self.battle_power
            
            return True
        return False
    
    def add_exp(self, exp_amount):
        """
        增加经验值
        
        参数:
            exp_amount: 经验值数量
        """
        self.exp += exp_amount
        
        # 检查是否可以升级
        while self.level_up():
            print(f"{self.name} 升级到 {self.level} 级！")
    
    def add_currency(self, amount):
        """
        增加货币（金币）
        
        参数:
            amount: 货币数量
        """
        self.gold += amount
        self.currencies['gold'] = self.gold
    
    def add_points(self, amount):
        """
        增加积分
        
        参数:
            amount: 积分数量
        """
        self.currencies['points'] = self.currencies.get('points', 0) + amount
    
    def get_points(self):
        """
        获取当前积分
        
        返回:
            当前积分数量
        """
        return self.currencies.get('points', 0)
    
    def spend_points(self, amount):
        """
        消费积分
        
        参数:
            amount: 消费的积分数量
            
        返回:
            bool: 是否消费成功
        """
        current_points = self.currencies.get('points', 0)
        if current_points >= amount:
            self.currencies['points'] = current_points - amount
            return True
        return False
    
    def take_damage(self, damage, damage_type="physical", is_critical=False):
        """
        受到伤害
        
        参数:
            damage: 伤害值
            damage_type: 伤害类型 (physical/magic/tao/true)
            is_critical: 是否暴击
            
        返回:
            实际受到的伤害
        """
        # 应用防御减伤（如果不是真实伤害）
        if damage_type != "true":
            if damage_type == "physical":
                # 物理伤害应用物理防御
                defense_min = getattr(self, '防御下限', 0)
                defense_max = getattr(self, '防御上限', 0)
                if defense_min > 0 or defense_max > 0:
                    import random
                    defense = random.randint(defense_min, defense_max)
                    damage = max(1, damage - defense)
            elif damage_type in ["magic", "tao"]:
                # 魔法/道术伤害应用魔法防御
                magic_defense = getattr(self, '魔抗', 0)
                damage = max(1, damage - magic_defense)
        
        # 确保伤害至少为1
        actual_damage = max(1, damage)
        
        # 扣减生命值
        self.current_hp = max(0, self.current_hp - actual_damage)
        if self.current_hp <= 0:
            self.is_alive = False
            
        return actual_damage
    
    def heal(self, heal_amount):
        """
        恢复生命值
        
        参数:
            heal_amount: 恢复量
        """
        self.current_hp = min(self.max_hp, self.current_hp + heal_amount)
        if self.current_hp > 0:
            self.is_alive = True
    
    def calculate_attack(self, is_critical=False):
        """
        计算攻击伤害
        
        参数:
            is_critical: 是否暴击
            
        返回:
            攻击伤害值
        """
        from game.core.luck_system import LuckSystem
        
        # 获取攻击范围
        attack_min = getattr(self, '攻击下限', 0)
        attack_max = getattr(self, '攻击上限', 0)
        
        # 如果没有武器，使用基础攻击力
        if attack_min == 0 and attack_max == 0:
            attack_min = 1
            attack_max = 3
        
        # 获取玩家幸运值
        player_luck = self.get_total_luck()
        
        # 使用幸运值计算伤害
        damage = LuckSystem.calculate_damage_with_luck(attack_min, attack_max, player_luck)

        # 🔧 新增：应用被动技能伤害加成
        damage_multiplier, extra_damage = self.skill_manager.get_skill_damage_bonus(self)
        damage = int(damage * damage_multiplier + extra_damage)

        # 如果暴击，增加伤害
        if is_critical:
            critical_multiplier = getattr(self, 'critical_damage', 1.5)
            damage = int(damage * critical_multiplier)

        return damage
    
    def calculate_magic_attack(self, magic_power=1.0, is_critical=False):
        """
        计算魔法攻击伤害
        
        参数:
            magic_power: 技能魔法强度
            is_critical: 是否暴击
            
        返回:
            魔法攻击伤害值
        """
        from game.core.luck_system import LuckSystem
        
        # 获取魔法攻击范围
        magic_min = getattr(self, '魔法攻击下限', 0)
        magic_max = getattr(self, '魔法攻击上限', 0)
        
        # 如果没有魔法攻击力，使用基础值
        if magic_min == 0 and magic_max == 0:
            magic_min = 1
            magic_max = 2
        
        # 获取玩家幸运值
        player_luck = self.get_total_luck()
        
        # 使用幸运值计算基础伤害
        base_damage = LuckSystem.calculate_damage_with_luck(magic_min, magic_max, player_luck)
        damage = base_damage * magic_power

        # 🔧 新增：应用被动技能伤害加成
        damage_multiplier, extra_damage = self.skill_manager.get_skill_damage_bonus(self)
        damage = int(damage * damage_multiplier + extra_damage)

        # 如果暴击，增加伤害
        if is_critical:
            critical_multiplier = getattr(self, 'critical_damage', 1.5)
            damage = int(damage * critical_multiplier)

        return int(damage)
    
    def calculate_tao_attack(self, tao_power=1.0, is_critical=False):
        """
        计算道术攻击伤害
        
        参数:
            tao_power: 技能道术强度
            is_critical: 是否暴击
            
        返回:
            道术攻击伤害值
        """
        from game.core.luck_system import LuckSystem
        
        # 获取道术攻击范围
        tao_min = getattr(self, '道术攻击下限', 0)
        tao_max = getattr(self, '道术攻击上限', 0)
        
        # 如果没有道术攻击力，使用基础值
        if tao_min == 0 and tao_max == 0:
            tao_min = 1
            tao_max = 2
        
        # 获取玩家幸运值
        player_luck = self.get_total_luck()
        
        # 使用幸运值计算基础伤害（这里实现了你提供的逻辑）
        base_damage = LuckSystem.calculate_damage_with_luck(tao_min, tao_max, player_luck)
        damage = base_damage * tao_power

        # 🔧 新增：应用被动技能伤害加成
        damage_multiplier, extra_damage = self.skill_manager.get_skill_damage_bonus(self)
        damage = int(damage * damage_multiplier + extra_damage)

        # 如果暴击，增加伤害
        if is_critical:
            critical_multiplier = getattr(self, 'critical_damage', 1.5)
            damage = int(damage * critical_multiplier)

        return int(damage)
    
    def get_max_hp(self):
        """
        获取最大生命值（兼容战斗系统）
        
        返回:
            最大生命值
        """
        return self.stats.get_max_hp()
    
    def get_max_mp(self):
        """
        获取最大魔法值（兼容战斗系统）
        
        返回:
            最大魔法值
        """
        return self.stats.get_max_mp()
    
    def get_attack_range(self):
        """
        获取攻击范围（为了兼容战斗系统）
        
        返回:
            tuple: (最小攻击力, 最大攻击力)
        """
        attack_min = getattr(self, '攻击下限', 0)
        attack_max = getattr(self, '攻击上限', 0)
        
        # 如果没有武器，使用基础攻击力
        if attack_min == 0 and attack_max == 0:
            attack_min = 1
            attack_max = 3
            
        return (attack_min, attack_max)
    
    def get_magic_range(self):
        """
        获取魔法攻击范围（为了兼容战斗系统）
        
        返回:
            tuple: (最小魔法攻击力, 最大魔法攻击力)
        """
        magic_min = getattr(self, '魔法攻击下限', 0)
        magic_max = getattr(self, '魔法攻击上限', 0)
        
        # 如果没有魔法攻击力，使用基础值
        if magic_min == 0 and magic_max == 0:
            magic_min = 1
            magic_max = 2
            
        return (magic_min, magic_max)
    
    def get_tao_range(self):
        """
        获取道术攻击范围（为了兼容战斗系统）
        
        返回:
            tuple: (最小道术攻击力, 最大道术攻击力)
        """
        tao_min = getattr(self, '道术攻击下限', 0)
        tao_max = getattr(self, '道术攻击上限', 0)
        
        # 如果没有道术攻击力，使用基础值
        if tao_min == 0 and tao_max == 0:
            tao_min = 1
            tao_max = 2
            
        return (tao_min, tao_max)
    
    def get_accuracy(self):
        """
        获取命中率（为了兼容战斗系统）
        
        返回:
            int: 命中值
        """
        return getattr(self, '准确', 10)
    
    def get_agility(self):
        """
        获取敏捷值（为了兼容战斗系统）
        
        返回:
            int: 敏捷值
        """
        return getattr(self, '敏捷', 10)
    
    def get_total_luck(self):
        """
        获取总幸运值（为了兼容战斗系统）
        
        返回:
            int: 幸运值
        """
        return min(9, max(0, getattr(self, '幸运', 0)))
    
    def get_total_curse(self):
        """
        获取总诅咒值（为了兼容战斗系统）
        
        返回:
            int: 诅咒值
        """
        return min(9, max(0, getattr(self, '诅咒', 0)))
    
    @property
    def critical_rate(self):
        """
        暴击率属性（为了兼容战斗系统）
        
        返回:
            float: 暴击率
        """
        return getattr(self, '暴击率', 0.05)
    
    @property
    def critical_damage(self):
        """
        暴击伤害倍率（为了兼容战斗系统）
        
        返回:
            float: 暴击伤害倍率
        """
        return getattr(self, '暴击伤害', 1.5)
    
    # stats作为属性而不是property，避免冲突
    
    def add_item_to_inventory(self, item, quantity=1):
        """
        添加物品到背包
        
        参数:
            item: 物品数据
            quantity: 数量
            
        返回:
            bool: 是否成功添加
        """
        return self.inventory_manager.add_item(item, quantity)
    
    def equip_item(self, item):
        """
        装备物品
        
        参数:
            item: 装备物品
            
        返回:
            bool: 是否成功装备
        """
        success = self.equipment_manager.equip_item(item, self)
        if success:
            # 更新属性
            self._load_stats_from_character_stats()
            
            # 重新计算战力
            self.battle_power = self._calculate_battle_power()
            self.combat_value = self.battle_power
        return success
    
    def unequip_item(self, slot):
        """
        卸下装备
        
        参数:
            slot: 装备槽位
            
        返回:
            bool: 是否成功卸下
        """
        success = self.equipment_manager.unequip_item(slot, self)
        if success:
            # 更新属性
            self._load_stats_from_character_stats()
            
            # 重新计算战力
            self.battle_power = self._calculate_battle_power()
            self.combat_value = self.battle_power
        return success
    
    def use_skill(self, skill_name, target=None):
        """
        使用技能
        
        参数:
            skill_name: 技能名称
            target: 目标
            
        返回:
            技能使用结果
        """
        return self.skill_manager.use_skill(skill_name, target, self)
    
    def accept_quest(self, quest_data):
        """
        接受任务
        
        参数:
            quest_data: 任务数据
            
        返回:
            bool: 是否成功接受
        """
        return self.quest_manager.accept_quest(quest_data, self)
    
    def complete_quest(self, quest_id):
        """
        完成任务
        
        参数:
            quest_id: 任务ID
            
        返回:
            奖励信息
        """
        return self.quest_manager.complete_quest(quest_id, self)
    
    def save_game(self, save_name="autosave"):
        """
        保存游戏
        
        参数:
            save_name: 存档名称
            
        返回:
            bool: 是否保存成功
        """
        return self.save_load_manager.save_player_data(self, save_name)
    
    def load_game(self, save_name="autosave"):
        """
        加载游戏
        
        参数:
            save_name: 存档名称
            
        返回:
            bool: 是否加载成功
        """
        data = self.save_load_manager.load_player_data(save_name)
        if data:
            return self.save_load_manager.apply_player_data(self, data)
        return False
    
    def get_player_info(self):
        """
        获取玩家信息
        
        返回:
            玩家信息字典
        """
        return {
            'basic_info': {
                'name': self.name,
                'class': self.character_class,
                'gender': self.gender,
                'level': self.level,
                'exp': self.exp,
                'exp_to_next': self.exp_to_next_level
            },
            'stats': self.stats.get_all_stats(),
            'resources': {
                'gold': self.gold,
                'current_hp': self.current_hp,
                'max_hp': self.hp,
                'current_mp': self.current_mp,
                'max_mp': self.mp
            },
            'battle_power': self.battle_power,
            'location': {
                'position': self.position,
                'current_map': self.current_map
            },
            'status': {
                'is_alive': self.is_alive,
                'in_battle': self.in_battle,
                'is_hunting': self.is_hunting,
                'auto_hunt_enabled': self.auto_hunt_enabled
            }
        }
    
    def to_dict(self):
        """
        转换为字典格式（兼容旧版本）
        
        返回:
            字典格式的玩家数据
        """
        return self.save_load_manager._collect_player_data(self)
    
    def add_monster_kill(self, monster_name, map_name=None, is_boss=False):
        """
        记录怪物击杀
        
        参数:
            monster_name: 怪物名称
            map_name: 地图名称（可选）
            is_boss: 是否为Boss
        """
        # 更新总击杀统计
        if is_boss:
            self.kill_statistics['total_bosses'] += 1
            print(f"🔥 击败Boss [{monster_name}]！总Boss击杀: {self.kill_statistics['total_bosses']}")
        else:
            self.kill_statistics['total_monsters'] += 1
        
        # 更新怪物类型统计
        if monster_name not in self.kill_statistics['monster_types']:
            self.kill_statistics['monster_types'][monster_name] = 0
        self.kill_statistics['monster_types'][monster_name] += 1
        
        # 更新地图击杀统计
        if map_name:
            if map_name not in self.kill_statistics['map_kills']:
                self.kill_statistics['map_kills'][map_name] = {'monsters': 0, 'bosses': 0}
            
            if is_boss:
                self.kill_statistics['map_kills'][map_name]['bosses'] += 1
            else:
                self.kill_statistics['map_kills'][map_name]['monsters'] += 1
        
        # 检查地图解锁进度
        self._check_map_unlock_progress()
    
    def _check_map_unlock_progress(self):
        """
        检查地图解锁进度
        """
        # 这里将在地图管理器中调用，用于检查是否满足解锁条件
        pass
    
    def get_kill_statistics(self):
        """
        获取击杀统计信息
        
        返回:
            击杀统计字典
        """
        return self.kill_statistics.copy()
    
    def get_map_kill_progress(self, map_name):
        """
        获取指定地图的击杀进度
        
        参数:
            map_name: 地图名称
            
        返回:
            该地图的击杀统计，如果没有则返回默认值
        """
        return self.kill_statistics['map_kills'].get(map_name, {'monsters': 0, 'bosses': 0})
    
    def can_unlock_map(self, map_unlock_requirements):
        """
        检查是否可以解锁指定地图
        
        参数:
            map_unlock_requirements: 地图解锁需求 {'monsters_needed': 数量, 'bosses_needed': 数量}
            
        返回:
            是否可以解锁
        """
        monsters_needed = map_unlock_requirements.get('monsters_needed', 0)
        bosses_needed = map_unlock_requirements.get('bosses_needed', 0)
        
        current_monsters = self.kill_statistics['total_monsters']
        current_bosses = self.kill_statistics['total_bosses']
        
        return current_monsters >= monsters_needed and current_bosses >= bosses_needed
    
    def get_attribute_display(self, attribute_name: str) -> str:
        """
        获取属性的显示值
        
        参数:
            attribute_name: 属性名称
            
        返回:
            属性显示字符串
        """
        # 定义默认值
        default_values = {
            'exp_bonus': '0%',
            'gold_bonus': '0%', 
            'drop_rate_bonus': '0%',
            'crit_bonus': '0%',
            'luck': '0',
            'curse': '0'
        }
        
        # 尝试获取玩家属性
        if hasattr(self, attribute_name):
            value = getattr(self, attribute_name)
            if attribute_name.endswith('_bonus') or attribute_name.endswith('_rate'):
                return f"{value:.1%}" if isinstance(value, (int, float)) else str(value)
            else:
                return str(value)
        
        # 返回默认值
        return default_values.get(attribute_name, '0')