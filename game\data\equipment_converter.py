import json
import random
import os
from typing import Dict, Any, List

# 从JSON文件加载前后缀配置
PREFIXES = {
    "RARE": [],
    "EPIC": [],
    "LEGENDARY": []
}

try:
    # 使用相对路径构建文件路径
    current_dir = os.path.dirname(__file__)
    config_path = os.path.join(current_dir, 'items_config.json')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = json.load(f)
        PREFIXES["RARE"] = config_data.get("rare_prefixes", [])
        PREFIXES["EPIC"] = config_data.get("epic_prefixes", [])
        PREFIXES["LEGENDARY"] = config_data.get("legendary_prefixes", [])
except (FileNotFoundError, json.JSONDecodeError) as e:
    print(f"警告: 无法加载或解析 items_config.json: {e}. 将使用空的前后缀列表。")


class EquipmentConverter:
    """
    一个用于转换和处理装备数据的工具类
    """
    @staticmethod
    def standardize_item_dict(item_dict: dict) -> dict:
        """
        将一个物品字典标准化，确保其具有嵌套的 'attributes' 结构。
        这主要用于兼容旧的、平铺属性格式的物品数据。

        Args:
            item_dict: 可能是旧格式的物品字典。

        Returns:
            一个保证具有 'attributes' 键的新字典。
        """
        if not isinstance(item_dict, dict):
            return item_dict  # 如果不是字典，直接返回

        new_dict = item_dict.copy()

        # 🔧 修复：处理已经有 attributes 字段的情况
        if 'attributes' in item_dict and isinstance(item_dict['attributes'], dict):
            attributes = item_dict['attributes'].copy()

            # 🔧 修复：如果 attributes 中有 stats 字段，将其提升到 attributes 级别
            if 'stats' in attributes and isinstance(attributes['stats'], dict):
                stats = attributes.pop('stats')
                # 将 stats 中的属性合并到 attributes 中
                for key, value in stats.items():
                    if key not in attributes:  # 避免覆盖已有的属性
                        attributes[key] = value
                new_dict['attributes'] = attributes

            return new_dict

        # 处理没有 attributes 字段的情况
        attributes = {}

        non_attribute_keys = [
            'name', 'type', 'category', 'id', 'description', 'icon_path',
            'quantity', 'stackable', 'max_stack', 'sell_price', 'level_requirement',
            'required_class', 'slot', 'durability', 'max_durability', 'rarity',
            'special', 'requirements', 'gender', 'effects', 'skill_level', 'attributes'
        ]

        keys_to_move = [key for key in new_dict if key not in non_attribute_keys]

        for key in keys_to_move:
            attributes[key] = new_dict.pop(key)

        new_dict['attributes'] = attributes
        return new_dict
        
    @staticmethod
    def convert_to_new_format(old_equipment_data):
        """
        将旧的装备数据列表转换为新的格式
        """
        new_data = []
        for item_name, item_info in old_equipment_data.items():
            new_item = {
                "name": item_name,
                "type": "equipment",
                "level": item_info.get("level", 1),
                "requirements": item_info.get("requirements", {}),
                "attributes": {}
            }
            for key, value in item_info.items():
                if key not in ["level", "requirements"]:
                    new_item["attributes"][key] = value
            new_data.append(new_item)
        return new_data

    @staticmethod
    def add_random_prefixes(item_name, rarity, attributes):
        """
        为装备添加随机前后缀
        """
        if rarity == 'rare':
            if not PREFIXES["RARE"]: return item_name, attributes
            prefix = random.choice(PREFIXES["RARE"])
            item_name = f"{prefix['name']}{item_name}"
            for attr, value in prefix['attributes'].items():
                attributes[attr] = attributes.get(attr, 0) + value
        elif rarity == 'epic':
            if not PREFIXES["EPIC"] or not PREFIXES["RARE"]: return item_name, attributes
            prefix1 = random.choice(PREFIXES["EPIC"])
            prefix2 = random.choice(PREFIXES["RARE"])
            item_name = f"{prefix1['name']}{prefix2['name']}{item_name}"
            for attr, value in prefix1['attributes'].items():
                attributes[attr] = attributes.get(attr, 0) + value
            for attr, value in prefix2['attributes'].items():
                attributes[attr] = attributes.get(attr, 0) + value
        elif rarity == 'legendary':
            if not PREFIXES["LEGENDARY"] or not PREFIXES["EPIC"] or not PREFIXES["RARE"]: return item_name, attributes
            prefix1 = random.choice(PREFIXES["LEGENDARY"])
            prefix2 = random.choice(PREFIXES["EPIC"])
            prefix3 = random.choice(PREFIXES["RARE"])
            item_name = f"{prefix1['name']}{prefix2['name']}{prefix3['name']}{item_name}"
            for attr, value in prefix1['attributes'].items():
                attributes[attr] = attributes.get(attr, 0) + value
            for attr, value in prefix2['attributes'].items():
                attributes[attr] = attributes.get(attr, 0) + value
            for attr, value in prefix3['attributes'].items():
                attributes[attr] = attributes.get(attr, 0) + value
        
        return item_name, attributes

    @staticmethod
    def get_stats_string(stats):
        """
        将属性字典转换为格式化的字符串
        """
        if not stats:
            return "无特殊属性"
        
        parts = []
        for key, value in stats.items():
            if isinstance(value, list) and len(value) == 2:
                parts.append(f"{key}: {value[0]}-{value[1]}")
            else:
                parts.append(f"{key}: {value}")
        return "  ".join(parts)

    @staticmethod
    def convert_item_stats(equipment):
        """
        将装备原始属性转换为标准字典格式
        
        Args:
            equipment: 装备对象或字典
            
        Returns:
            dict: 标准化的属性字典
        """
        if not equipment:
            return {}
        
        stats = {}
        
        # 支持 Item 对象和字典两种格式
        if hasattr(equipment, 'attributes'):
            # Item 对象
            attributes = equipment.attributes or {}
        elif isinstance(equipment, dict):
            # 字典格式
            attributes = equipment.get('attributes', {})
            # 也检查 stats 字段（装备管理器格式）
            if not attributes and 'stats' in equipment:
                attributes = equipment['stats']
        else:
            return {}
        
        # 定义属性映射（英文到标准键）
        attr_mapping = {
            # 攻击相关
            'attack': 'attack',
            'attack_min': 'attack',
            'attack_max': 'attack',
            '攻击': 'attack',
            '攻击力': 'attack',
            '攻击下限': 'attack',
            '攻击上限': 'attack',
            
            # 魔法攻击
            'magic_attack': 'magic',
            'magic_min': 'magic',
            'magic_max': 'magic',
            '魔法': 'magic',
            '魔法攻击': 'magic',
            '魔法下限': 'magic',
            '魔法上限': 'magic',
            
            # 道术攻击
            'taoism_attack': 'taoism',
            'tao_min': 'taoism',
            'tao_max': 'taoism',
            '道术': 'taoism',
            '道术攻击': 'taoism',
            '道术下限': 'taoism',
            '道术上限': 'taoism',
            
            # 防御相关
            'defense': 'defense',
            '防御': 'defense',
            '防御力': 'defense',
            'armor': 'defense',
            
            # 魔法防御
            'magic_defense': 'magic_defense',
            '魔法防御': 'magic_defense',
            '魔防': 'magic_defense',
            
            # 生命值和魔法值
            'hp': 'hp',
            'health': 'hp',
            '生命': 'hp',
            '生命值': 'hp',
            'mp': 'mp',
            'mana': 'mp',
            '魔法': 'mp',
            '魔法值': 'mp',
            
            # 其他属性
            'agility': 'agility',
            '敏捷': 'agility',
            'accuracy': 'accuracy',
            '准确': 'accuracy',
            'critical': 'critical',
            '暴击': 'critical',
            'luck': 'luck',
            '幸运': 'luck',
        }
        
        # 处理属性
        for attr_name, value in attributes.items():
            if value == 0:
                continue
                
            # 映射属性名
            standard_key = attr_mapping.get(attr_name, attr_name)
            
            # 处理范围值
            if standard_key in ['attack', 'magic', 'taoism']:
                # 检查是否已经有最小值和最大值
                min_key = attr_name + '_min' if not attr_name.endswith(('下限', '上限', '_min', '_max')) else attr_name
                max_key = attr_name + '_max' if not attr_name.endswith(('下限', '上限', '_min', '_max')) else attr_name
                
                if '下限' in attr_name or '_min' in attr_name:
                    if standard_key not in stats:
                        stats[standard_key] = [value, value]
                    else:
                        stats[standard_key][0] = value
                elif '上限' in attr_name or '_max' in attr_name:
                    if standard_key not in stats:
                        stats[standard_key] = [stats.get(standard_key, [0, 0])[0], value]
                    else:
                        stats[standard_key][1] = value
                else:
                    # 如果是单个值，同时设置下限和上限
                    if isinstance(value, (int, float)):
                        stats[standard_key] = [value, value]
                    elif isinstance(value, list) and len(value) == 2:
                        stats[standard_key] = value

            else:
                stats[standard_key] = value
        
        return stats

# 便捷函数
def get_equipment_converter():
    return EquipmentConverter()