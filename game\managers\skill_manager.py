# -*- coding: utf-8 -*-
"""
技能管理器
负责管理玩家的技能系统，包括技能学习、升级、使用等功能
"""

from typing import Dict, List, Optional, Any
import time
import json


class SkillManager:
    """
    技能管理器类
    负责管理玩家的技能系统
    """
    
    def __init__(self):
        """
        初始化技能管理器
        """
        # 已学习的技能列表
        self.learned_skills: List[Dict[str, Any]] = []
        
        # 技能冷却时间记录
        self.skill_cooldowns: Dict[str, float] = {}
        
        # 技能经验值
        self.skill_experience: Dict[str, int] = {}
    
    def learn_skill(self, skill_data: Dict[str, Any], player) -> bool:
        """
        学习技能
        
        参数:
            skill_data: 技能数据
            player: 玩家对象
            
        返回:
            bool: 是否成功学习
        """
        if not skill_data:
            return False
        
        skill_name = skill_data.get('name')
        if not skill_name:
            return False
        
        # 检查是否已经学会该技能
        if self.has_skill(skill_name):
            return False
        
        # 检查职业限制
        if not self._check_class_requirement(skill_data, player):
            return False
        
        # 检查等级限制
        if not self._check_level_requirement(skill_data, player):
            return False
        
        # 检查前置技能
        if not self._check_prerequisite_skills(skill_data):
            return False
        
        # 🔧 新增：检查技能书要求
        if not self._check_skill_book_requirement(skill_name, player):
            return False
        
        # 🔧 新增：消耗技能书
        if not self._consume_skill_book(skill_name, player):
            return False
        
        # 学习技能
        new_skill = skill_data.copy()
        new_skill['level'] = 1
        new_skill['learned_at'] = time.time()
        
        self.learned_skills.append(new_skill)
        self.skill_experience[skill_name] = 0
        
        return True
    
    def upgrade_skill(self, skill_name: str, player) -> bool:
        """
        升级技能
        
        参数:
            skill_name: 技能名称
            player: 玩家对象
            
        返回:
            bool: 是否成功升级
        """
        skill = self.get_skill(skill_name)
        if not skill:
            return False
        
        current_level = skill.get('level', 1)
        max_level = skill.get('max_level', 10)
        
        # 检查是否已达到最大等级
        if current_level >= max_level:
            return False
        
        # 检查升级所需经验
        required_exp = self._calculate_required_exp(skill_name, current_level + 1)
        current_exp = self.skill_experience.get(skill_name, 0)
        
        if current_exp < required_exp:
            return False
        
        # 检查升级所需等级
        required_player_level = skill.get('level_requirement', {}).get(current_level + 1, 0)
        if player.level < required_player_level:
            return False
        
        # 升级技能
        skill['level'] = current_level + 1
        self.skill_experience[skill_name] -= required_exp
        
        return True
    
    def use_skill(self, skill_name: str, target=None, player=None) -> Dict[str, Any]:
        """
        使用技能
        
        参数:
            skill_name: 技能名称
            target: 目标对象
            player: 玩家对象
            
        返回:
            技能使用结果字典
        """
        result = {
            'success': False,
            'message': '',
            'effects': {},
            'damage': 0,
            'healing': 0
        }
        
        skill = self.get_skill(skill_name)
        if not skill:
            result['message'] = f'未学会技能：{skill_name}'
            return result
        
        # 检查冷却时间
        if not self._check_cooldown(skill_name):
            cooldown_remaining = self._get_cooldown_remaining(skill_name)
            result['message'] = f'技能冷却中，剩余时间：{cooldown_remaining:.1f}秒'
            return result
        
        # 检查魔法值消耗
        mp_cost = skill.get('mp_cost', 0)
        if player and hasattr(player, 'current_mp'):
            if player.current_mp < mp_cost:
                result['message'] = '魔法值不足'
                return result
        
        # 执行技能效果
        skill_type = skill.get('type', 'active')
        skill_category = skill.get('category', 'attack')
        
        if skill_category == 'attack':
            result = self._execute_attack_skill(skill, target, player)
        elif skill_category == 'heal':
            result = self._execute_heal_skill(skill, target, player)
        elif skill_category == 'buff':
            result = self._execute_buff_skill(skill, target, player)
        elif skill_category == 'debuff':
            result = self._execute_debuff_skill(skill, target, player)
        
        # 如果技能使用成功
        if result['success']:
            # 消耗魔法值
            if player and hasattr(player, 'current_mp'):
                player.current_mp = max(0, player.current_mp - mp_cost)
            
            # 设置冷却时间
            cooldown = skill.get('cooldown', 0)
            if cooldown > 0:
                self.skill_cooldowns[skill_name] = time.time() + cooldown
            
            # 增加技能经验
            self._add_skill_experience(skill_name, 1)
        
        return result
    
    def _execute_attack_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行攻击技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 计算技能伤害
        base_damage = skill.get('base_damage', 0)
        damage_multiplier = skill.get('damage_multiplier', 1.0)
        skill_level = skill.get('level', 1)
        
        # 基础伤害计算
        if player:
            # 根据技能类型使用不同的攻击力
            skill_attack_type = skill.get('attack_type', 'physical')
            if skill_attack_type == 'physical':
                player_attack = (getattr(player, '攻击下限', 0) + getattr(player, '攻击上限', 0)) / 2
            elif skill_attack_type == 'magic':
                player_attack = (getattr(player, '魔法攻击下限', 0) + getattr(player, '魔法攻击上限', 0)) / 2
            elif skill_attack_type == 'tao':
                player_attack = (getattr(player, '道术攻击下限', 0) + getattr(player, '道术攻击上限', 0)) / 2
            else:
                player_attack = 0
            
            total_damage = int((base_damage + player_attack * damage_multiplier) * (1 + skill_level * 0.1))
        else:
            total_damage = int(base_damage * (1 + skill_level * 0.1))
        
        result['damage'] = total_damage
        result['message'] = f'使用{skill["name"]}造成{total_damage}点伤害'
        
        return result
    
    def _execute_heal_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行治疗技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 计算治疗量
        base_healing = skill.get('base_healing', 0)
        healing_multiplier = skill.get('healing_multiplier', 1.0)
        skill_level = skill.get('level', 1)
        
        if player:
            # 治疗技能通常基于道术攻击力
            tao_attack = (getattr(player, '道术攻击下限', 0) + getattr(player, '道术攻击上限', 0)) / 2
            total_healing = int((base_healing + tao_attack * healing_multiplier) * (1 + skill_level * 0.1))
        else:
            total_healing = int(base_healing * (1 + skill_level * 0.1))
        
        result['healing'] = total_healing
        result['message'] = f'使用{skill["name"]}恢复{total_healing}点生命值'
        
        return result
    
    def _execute_buff_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行增益技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 获取增益效果
        buff_effects = skill.get('buff_effects', {})
        duration = skill.get('duration', 60)  # 默认60秒
        
        result['effects'] = {
            'type': 'buff',
            'effects': buff_effects,
            'duration': duration
        }
        result['message'] = f'使用{skill["name"]}获得增益效果'
        
        return result
    
    def _execute_debuff_skill(self, skill: Dict[str, Any], target, player) -> Dict[str, Any]:
        """
        执行减益技能
        
        参数:
            skill: 技能数据
            target: 目标
            player: 玩家
            
        返回:
            技能执行结果
        """
        result = {'success': True, 'message': '', 'effects': {}, 'damage': 0, 'healing': 0}
        
        # 获取减益效果
        debuff_effects = skill.get('debuff_effects', {})
        duration = skill.get('duration', 30)  # 默认30秒
        
        result['effects'] = {
            'type': 'debuff',
            'effects': debuff_effects,
            'duration': duration
        }
        result['message'] = f'使用{skill["name"]}对目标施加减益效果'
        
        return result
    
    def _check_class_requirement(self, skill_data: Dict[str, Any], player) -> bool:
        """
        检查职业限制
        
        参数:
            skill_data: 技能数据
            player: 玩家对象
            
        返回:
            bool: 是否满足职业要求
        """
        required_class = skill_data.get('required_class')
        if not required_class:
            return True
        
        if isinstance(required_class, str):
            return player.character_class == required_class
        elif isinstance(required_class, list):
            return player.character_class in required_class
        
        return False
    
    def _check_level_requirement(self, skill_data: Dict[str, Any], player) -> bool:
        """
        检查等级限制
        
        参数:
            skill_data: 技能数据
            player: 玩家对象
            
        返回:
            bool: 是否满足等级要求
        """
        required_level = skill_data.get('required_level', 1)
        return player.level >= required_level
    
    def _check_prerequisite_skills(self, skill_data: Dict[str, Any]) -> bool:
        """
        检查前置技能
        
        参数:
            skill_data: 技能数据
            
        返回:
            bool: 是否满足前置技能要求
        """
        prerequisites = skill_data.get('prerequisites', [])
        if not prerequisites:
            return True
        
        for prereq in prerequisites:
            prereq_name = prereq.get('name')
            prereq_level = prereq.get('level', 1)
            
            skill = self.get_skill(prereq_name)
            if not skill or skill.get('level', 0) < prereq_level:
                return False
        
        return True
    
    def _check_cooldown(self, skill_name: str) -> bool:
        """
        检查技能冷却时间
        
        参数:
            skill_name: 技能名称
            
        返回:
            bool: 是否可以使用
        """
        if skill_name not in self.skill_cooldowns:
            return True
        
        return time.time() >= self.skill_cooldowns[skill_name]
    
    def _get_cooldown_remaining(self, skill_name: str) -> float:
        """
        获取技能剩余冷却时间
        
        参数:
            skill_name: 技能名称
            
        返回:
            剩余冷却时间（秒）
        """
        if skill_name not in self.skill_cooldowns:
            return 0.0
        
        remaining = self.skill_cooldowns[skill_name] - time.time()
        return max(0.0, remaining)
    
    def _calculate_required_exp(self, skill_name: str, target_level: int) -> int:
        """
        计算技能升级所需经验
        
        参数:
            skill_name: 技能名称
            target_level: 目标等级
            
        返回:
            所需经验值
        """
        # 简单的经验计算公式
        return target_level * 100
    
    def _add_skill_experience(self, skill_name: str, exp: int):
        """
        增加技能经验
        
        参数:
            skill_name: 技能名称
            exp: 经验值
        """
        if skill_name not in self.skill_experience:
            self.skill_experience[skill_name] = 0
        
        self.skill_experience[skill_name] += exp
    
    def has_skill(self, skill_name: str) -> bool:
        """
        检查是否拥有技能
        
        参数:
            skill_name: 技能名称
            
        返回:
            bool: 是否拥有该技能
        """
        return any(skill.get('name') == skill_name for skill in self.learned_skills)
    
    def get_skill(self, skill_name: str) -> Optional[Dict[str, Any]]:
        """
        获取技能信息
        
        参数:
            skill_name: 技能名称
            
        返回:
            技能数据或None
        """
        for skill in self.learned_skills:
            if skill.get('name') == skill_name:
                return skill.copy()
        return None
    
    def get_all_skills(self) -> List[Dict[str, Any]]:
        """
        获取所有已学技能
        
        返回:
            技能列表
        """
        return self.learned_skills.copy()
    
    def get_skills_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        获取指定类别的技能
        
        参数:
            category: 技能类别
            
        返回:
            技能列表
        """
        return [skill for skill in self.learned_skills if skill.get('category') == category]
    
    def get_skill_cooldowns(self) -> Dict[str, float]:
        """
        获取所有技能的冷却时间
        
        返回:
            技能冷却时间字典
        """
        current_time = time.time()
        cooldowns = {}
        
        for skill_name, cooldown_end in self.skill_cooldowns.items():
            remaining = max(0.0, cooldown_end - current_time)
            if remaining > 0:
                cooldowns[skill_name] = remaining
        
        return cooldowns
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式（用于保存）
        
        返回:
            字典格式的技能数据
        """
        return {
            'learned_skills': self.learned_skills,
            'skill_cooldowns': self.skill_cooldowns,
            'skill_experience': self.skill_experience
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """
        从字典格式加载（用于读取存档）
        
        参数:
            data: 字典格式的技能数据
        """
        self.learned_skills = data.get('learned_skills', [])
        self.skill_cooldowns = data.get('skill_cooldowns', {})
        self.skill_experience = data.get('skill_experience', {})
    
    def _check_skill_book_requirement(self, skill_name: str, player) -> bool:
        """
        检查技能书要求
        
        参数:
            skill_name: 技能名称
            player: 玩家对象
            
        返回:
            bool: 是否拥有对应的技能书
        """
        # 🔧 修改：所有技能都需要技能书，不再有例外
        # 检查玩家是否拥有对应的技能书
        if hasattr(player, 'inventory_manager') and player.inventory_manager:
            skill_book_name = f"{skill_name}"  # 技能书名称与技能名称相同
            return player.inventory_manager.has_item(skill_book_name, 1)
        
        return False
    
    def _consume_skill_book(self, skill_name: str, player) -> bool:
        """
        消耗技能书
        
        参数:
            skill_name: 技能名称
            player: 玩家对象
            
        返回:
            bool: 是否成功消耗
        """
        # 🔧 修改：所有技能都需要消耗技能书，不再有例外
        # 消耗对应的技能书
        if hasattr(player, 'inventory_manager') and player.inventory_manager:
            skill_book_name = f"{skill_name}"  # 技能书名称与技能名称相同
            success = player.inventory_manager.remove_item(skill_book_name, 1)
            if success:
                print(f"📚 消耗技能书: {skill_book_name}")
                return True
            else:
                print(f"❌ 技能书不足: {skill_book_name}")
                return False
        
        return False