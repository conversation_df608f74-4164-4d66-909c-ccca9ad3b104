import pygame
import random
import os
import math # 用于距离计算和地图缩放
import tkinter as tk
from game.ui.ui_panel import UIPanel
from game.ui.damage_text_manager import DamageTextManager
from game.core.resource_manager import get_game_asset_path




class BattlePanel(UIPanel):
    """
    战斗面板，显示战斗情况，并包含一个小地图用于寻怪
    """
    def __init__(self, screen, battle_manager, player, position, size, map_manager=None, potion_effects_manager=None):
        super().__init__(screen, position, size)

        self.battle_manager = battle_manager
        self.player = player
        self.map_manager = map_manager  # 添加地图管理器引用
        self.potion_effects_manager = potion_effects_manager  # 药水效果管理器引用

        if hasattr(self.battle_manager, 'set_player_instance'):
            self.battle_manager.set_player_instance(self.player)

        self.title = "战斗区"
        self.background_color = (20, 20, 30)
        self.player_position = (self.rect.left + 100, self.rect.top + 200)
        self.enemy_positions = [
            (self.rect.left + 350, self.rect.top + 100),
            (self.rect.left + 400, self.rect.top + 200),
            (self.rect.left + 350, self.rect.top + 300)
        ]
        self.battle_effects = []
        self.damage_numbers = []
        
        # 创建伤害文本管理器
        self.damage_text_manager = DamageTextManager()

        self.progress_bar_height = 8
        self.progress_bar_width = 60  # 🔧 修改为和血量法力值进度条一样的宽度
        self.progress_bar_bg_color = (50, 50, 50)
        self.progress_bar_fill_color = (0, 150, 255)
        self.progress_bar_border_color = (100, 100, 100)

        self.create_skill_buttons()
        self.font = pygame.font.SysFont("SimHei", 16)
        self.small_font = pygame.font.SysFont("SimHei", 12)
        self.load_player_image()
        
        # Boss刷新状态现在使用独立的可移动窗口，不再需要pygame面板
        self.boss_refresh_panel = None

        # --- 小地图初始化 ---
        self.minimap_size = (150, 120)  # 增大小地图尺寸，更好展示分布效果
        self.minimap_padding = 5        
        self.minimap_rect = pygame.Rect(
            self.rect.right - self.minimap_size[0] - 20,                       # 移动到右上角
            self.rect.top + 20,                                                # 距离顶部20像素
            self.minimap_size[0],
            self.minimap_size[1]
        )
        self.minimap_bg_color = (30, 30, 40, 200) # 半透明背景 (R, G, B, Alpha)
        self.minimap_border_color = (80, 80, 100)
        self.minimap_player_color = (255, 255, 255) # 白色
        self.minimap_monster_color = (255, 0, 0)   # 红色
        self.minimap_target_line_color = (255, 255, 0) # 黄色
        self.minimap_player_dot_radius = 3
        self.minimap_monster_dot_radius = 2
        # --- 小地图初始化结束 ---
        
        # 自动吃药配置 - 改进版，从配置文件加载
        self.auto_potion_enabled = False  # 自动吃药总开关
        self.hp_threshold = 50  # HP自动使用阈值(百分比)
        self.mp_threshold = 30  # MP自动使用阈值(百分比)
        self.potion_cooldown = 3000  # 药水使用冷却时间(毫秒)
        self.last_potion_use_time = 0  # 上次使用药水的时间
        
        # 🔧 新增：加载保存的药水配置
        self.load_saved_potion_config()
        
        # 加载药水数据
        self.load_potion_data()
        
        # 用于防止死亡信息重复刷屏的状态跟踪
        self.last_death_message_time = 0
        self.death_message_cooldown = 5000  # 5秒内不重复显示死亡信息

    def load_potion_data(self):
        """加载药水数据"""
        try:
            import json
            import os
            
            config_path = "game/data/items_config.json"
            if not os.path.exists(config_path):
                print(f"药水配置文件不存在: {config_path}")
                self.hp_potions = []
                self.mp_potions = []
                self.both_potions = []
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                items_config = json.load(f)
            
            # 分类药水
            self.hp_potions = []  # 只恢复HP的药水
            self.mp_potions = []  # 只恢复MP的药水
            self.both_potions = []  # 同时恢复HP和MP的药水
            
            for item_name, item_data in items_config.items():
                if item_data.get('category') == 'potion':
                    effects = item_data.get('effects', {})
                    hp_restore = effects.get('hp_restore', 0)
                    mp_restore = effects.get('mp_restore', 0)
                    
                    if hp_restore > 0 and mp_restore > 0:
                        self.both_potions.append(item_name)
                    elif hp_restore > 0:
                        self.hp_potions.append(item_name)
                    elif mp_restore > 0:
                        self.mp_potions.append(item_name)
            
            print(f"加载药水数据完成: HP药水{len(self.hp_potions)}个, MP药水{len(self.mp_potions)}个, 双效药水{len(self.both_potions)}个")
            
        except Exception as e:
            print(f"加载药水数据时出错: {e}")
            self.hp_potions = []
            self.mp_potions = []
            self.both_potions = []
    
    def load_saved_potion_config(self):
        """
        加载保存的药水配置
        从药水配置文件中加载设置，实现配置持久化
        """
        try:
            import json
            import os
            from game.core.resource_manager import get_game_data_path
            
            config_path = get_game_data_path("potion_config.json")
            if not os.path.exists(config_path):
                print("📁 药水配置文件不存在，使用默认智能配置")
                self._create_smart_default_config()
                return
            
            # 加载保存的配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 应用配置到战斗面板
            self.hp_threshold = config_data.get("hp_threshold", 50)
            self.mp_threshold = config_data.get("mp_threshold", 30)
            self.auto_potion_enabled = config_data.get("auto_potion_enabled", False)
            
            print(f"✅ 药水配置已加载到战斗面板: HP阈值{self.hp_threshold}%, MP阈值{self.mp_threshold}%, 自动药水{'启用' if self.auto_potion_enabled else '禁用'}")
            
        except Exception as e:
            print(f"❌ 加载药水配置到战斗面板失败: {e}")
            self._create_smart_default_config()
    
    def _create_smart_default_config(self):
        """
        创建智能默认配置
        为新用户提供开箱即用的药水配置
        """
        print("🔧 创建智能默认药水配置...")
        
        # 设置合理的默认值
        self.hp_threshold = 50  # HP低于50%时自动使用药水
        self.mp_threshold = 30  # MP低于30%时自动使用药水
        self.auto_potion_enabled = True  # 默认启用自动药水
        
        # 保存默认配置
        self._save_potion_config_to_file()
        
        print(f"✅ 智能默认配置已创建: HP阈值{self.hp_threshold}%, MP阈值{self.mp_threshold}%, 自动药水已启用")
    
    def _save_potion_config_to_file(self):
        """
        保存药水配置到文件
        确保配置持久化
        """
        try:
            import json
            import os
            from game.core.resource_manager import get_game_data_path
            
            config_data = {
                "hp_threshold": self.hp_threshold,
                "mp_threshold": self.mp_threshold,
                "auto_potion_enabled": self.auto_potion_enabled,
                "enabled_potions": [1]  # 默认启用第一个药水
            }
            
            # 确保目录存在
            config_dir = "game/data"
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            config_path = get_game_data_path("potion_config.json")
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 药水配置已保存: {config_path}")
            
        except Exception as e:
            print(f"❌ 保存药水配置失败: {e}")

    def create_skill_buttons(self):
        self.clear_buttons()
        # 移除普通攻击和技能攻击按钮，因为现在是自动释放
        # 保留自动战斗按钮、Boss刷新按钮，并添加自动技能按钮
        self.add_auto_battle_button()
        self.add_boss_refresh_button()
        self.add_auto_skill_button()  # 新增自动技能按钮

    def load_player_image(self):
        try:
            character_class = getattr(self.player, 'character_class', '战士')
            gender = getattr(self.player, 'gender', '男')
            image_filename = f"{character_class}_{gender}.png"
            image_path = get_game_asset_path(f"images/characters/{image_filename}")
            if os.path.exists(image_path):
                self.player_image = pygame.image.load(image_path).convert_alpha() # 添加 convert_alpha()
                self.player_image = pygame.transform.scale(self.player_image, (40, 40))
            else:
                print(f"角色图片不存在: {image_path}")
                self.player_image = None
        except Exception as e:
            print(f"加载角色图片失败: {e}")
            self.player_image = None

    def update(self):
        super().update()
        self._update_battle_effects()
        self._update_damage_numbers()
        self.update_auto_battle_button()
        
        # 更新伤害文本动画
        self.damage_text_manager.update()
        
        # 更新战斗系统 - 这是关键的调用！
        current_time = pygame.time.get_ticks()
        self.battle_manager.update_battle(current_time)
        
        if hasattr(self.battle_manager, 'update_auto_hunt'):
            delta_time = 1.0 / 60.0 # 假设60FPS
            self.battle_manager.update_auto_hunt(delta_time)
        
        # 检查自动吃药
        if self.auto_potion_enabled:
            self.check_auto_potion(current_time)

    def _update_battle_effects(self):
        for effect in self.battle_effects[:]:
            effect["duration"] -= 1
            if effect["duration"] <= 0:
                self.battle_effects.remove(effect)

    def _update_damage_numbers(self):
        for damage in self.damage_numbers[:]:
            damage["offset"] += 1
            damage["duration"] -= 1
            if damage["duration"] <= 0:
                self.damage_numbers.remove(damage)

    def add_random_battle_effect(self):
        enemies = self.battle_manager.get_enemies()
        if enemies:
            enemy_position = random.choice(self.enemy_positions[:len(enemies)])
            effect_type = random.choice(["attack", "skill"])
            if effect_type == "attack":
                effect = {"type": "attack", "start_pos": self.player_position, "end_pos": enemy_position, "duration": 10}
            else:
                effect = {"type": "skill", "position": enemy_position, "radius": 30, "duration": 15}
            self.battle_effects.append(effect)

    def add_damage_number(self, damage, position, is_critical=False):
        damage_info = {"damage": damage, "position": position, "offset": 0, "duration": 60, "is_critical": is_critical}
        self.damage_numbers.append(damage_info)
    
    def show_player_damage(self, damage, is_critical=False):
        """显示玩家受到的伤害"""
        position = (self.player_position[0], self.player_position[1] - 20)
        self.damage_text_manager.add_player_damage(damage, position, is_critical)
    
    def show_enemy_damage(self, damage, enemy_index=0, is_critical=False):
        """显示敌人受到的伤害"""
        if enemy_index < len(self.enemy_positions):
            position = (
                self.enemy_positions[enemy_index][0], 
                self.enemy_positions[enemy_index][1] - 20
            )
            self.damage_text_manager.add_enemy_damage(damage, position, is_critical)
    


    def render(self):
        super().render() # 渲染面板背景和标题

        # 首先检查是否需要显示复活倒计时
        revival_info = self.battle_manager.get_revival_info() if hasattr(self.battle_manager, 'get_revival_info') else None
        
        if revival_info:
            # 玩家死亡，显示复活倒计时界面
            self.render_revival_countdown(revival_info)
        else:
            # 正常战斗界面
            # --- 渲染主要战斗场景元素 ---
            self.render_player()
            self.render_enemies()
            self.render_battle_effects()
            self.render_damage_numbers()
            
            # 渲染新的伤害文本动画
            self.damage_text_manager.render(self.screen)
            # --- 主要战斗场景元素结束 ---

            # --- 渲染小地图 ---
            self.render_minimap()
            # --- 小地图渲染结束 ---
            
            # 🔧 新增：显示战斗调试信息
            self.render_battle_debug_info()
            
            # 显示战斗倒计时
            self.render_battle_countdown()
        
        # Boss刷新状态窗口现在是独立的可移动窗口，不在此处绘制

    def render_battle_debug_info(self):
        """渲染战斗调试信息，帮助用户了解当前状态 - 只显示真实战斗信息"""
        debug_y = self.rect.top + 30  # 调整起始位置，为地图标签留出空间
        
        try:
            # 🗺️ 地图标签 - 显示当前地图
            current_map_name = "未知地图"
            if self.map_manager:
                if hasattr(self.map_manager, 'current_map_name'):
                    current_map_name = self.map_manager.current_map_name
                elif hasattr(self.map_manager, 'current_map') and self.map_manager.current_map:
                    current_map_name = getattr(self.map_manager.current_map, 'name', current_map_name)
            
            map_text = f"地图: {current_map_name}"
            map_surface = self.font.render(map_text, True, (200, 200, 255))  # 使用稍大的字体，浅蓝色
            self.screen.blit(map_surface, (self.rect.left + 10, debug_y))
            debug_y += 25  # 地图标签下方留更多空间
            
            # 战斗状态信息
            is_in_battle = getattr(self.battle_manager, 'is_in_battle', False)
            battle_status = "真实战斗中" if is_in_battle else "待机"
            
            status_text = f"状态: {battle_status}"
            status_color = (100, 255, 100) if is_in_battle else (255, 255, 100)
            status_surface = self.small_font.render(status_text, True, status_color)
            self.screen.blit(status_surface, (self.rect.left + 10, debug_y))
            debug_y += 15
            
            # 当前目标怪物信息（只在战斗时显示）
            if is_in_battle and hasattr(self.battle_manager, 'current_enemy') and self.battle_manager.current_enemy:
                enemy = self.battle_manager.current_enemy
                enemy_name = getattr(enemy, 'name', '未知')
                
                enemy_info = f"目标: {enemy_name}"
                enemy_surface = self.small_font.render(enemy_info, True, (255, 100, 100))
                self.screen.blit(enemy_surface, (self.rect.left + 10, debug_y))
                debug_y += 15
            
            # 自动战斗状态
            auto_status = self.battle_manager.get_auto_battle_status() if hasattr(self.battle_manager, 'get_auto_battle_status') else "未知"
            auto_text = f"自动: {auto_status}"
            auto_surface = self.small_font.render(auto_text, True, (200, 200, 255))
            self.screen.blit(auto_surface, (self.rect.left + 10, debug_y))
            debug_y += 20
            
            # 药水状态显示（新增）
            self.render_potion_status(self.rect.left + 10, debug_y)
            
        except Exception as e:
            error_text = f"调试信息错误: {str(e)[:30]}"
            error_surface = self.small_font.render(error_text, True, (255, 100, 100))
            self.screen.blit(error_surface, (self.rect.left + 10, debug_y))
    
    def render_potion_status(self, x, y):
        """渲染药水状态信息"""
        try:
            if not hasattr(self, 'potion_effects_manager') or not self.potion_effects_manager:
                return
            
            line_height = 14
            current_y = y
            
            # 获取冷却状态
            cooldowns = self.potion_effects_manager.get_all_cooldowns()
            if cooldowns:
                # 显示冷却中的药水
                cooldown_text = "💊 药水冷却:"
                cooldown_surface = self.small_font.render(cooldown_text, True, (255, 200, 100))
                self.screen.blit(cooldown_surface, (x, current_y))
                current_y += line_height
                
                for potion_name, remaining in cooldowns.items():
                    if remaining > 0:
                        cd_text = f"  {potion_name}: {remaining:.1f}s"
                        cd_surface = self.small_font.render(cd_text, True, (255, 150, 150))
                        self.screen.blit(cd_surface, (x, current_y))
                        current_y += line_height
            
            # 获取激活效果
            active_effects = self.potion_effects_manager.get_active_effects()
            if active_effects:
                # 显示正在恢复的效果
                effect_text = "🔄 缓慢恢复:"
                effect_surface = self.small_font.render(effect_text, True, (100, 255, 100))
                self.screen.blit(effect_surface, (x, current_y))
                current_y += line_height
                
                for effect in active_effects:
                    progress_percent = int(effect['progress'] * 100)
                    remaining = effect['remaining']
                    effect_type = "HP" if effect['type'] == 'hp' else "MP"
                    
                    effect_text = f"  {effect_type}: -{remaining} ({progress_percent}%)"
                    effect_color = (100, 255, 100) if effect['type'] == 'hp' else (100, 150, 255)
                    effect_surface = self.small_font.render(effect_text, True, effect_color)
                    self.screen.blit(effect_surface, (x, current_y))
                    current_y += line_height
                    
                    # 绘制进度条
                    progress_bar_width = 80
                    progress_bar_height = 3
                    progress_x = x + 15
                    progress_y = current_y
                    
                    # 背景
                    pygame.draw.rect(self.screen, (50, 50, 50), 
                                   (progress_x, progress_y, progress_bar_width, progress_bar_height))
                    
                    # 进度
                    progress_width = int(progress_bar_width * effect['progress'])
                    pygame.draw.rect(self.screen, effect_color, 
                                   (progress_x, progress_y, progress_width, progress_bar_height))
                    
                    current_y += progress_bar_height + 8
            
        except Exception as e:
            error_text = f"药水状态错误: {str(e)[:20]}"
            error_surface = self.small_font.render(error_text, True, (255, 100, 100))
            self.screen.blit(error_surface, (x, y))

    def _world_to_minimap_coords(self, relative_dx, relative_dy, detection_radius):
        """
        将相对世界坐标 (距离玩家的 dx, dy) 转换为小地图屏幕坐标。
        小地图以玩家为中心。
        世界单位的 detection_radius 对应于小地图的边缘。
        """
        if detection_radius == 0: # 避免除以零
            return self.minimap_rect.center # 如果侦测半径为0，返回小地图中心

        # 缩放因子: 每个世界单位对应多少小地图像素。
        # 我们希望侦测半径能容纳在小地图较小尺寸的一半内。
        effective_minimap_radius_pixels = min(self.minimap_rect.width, self.minimap_rect.height) / 2.0
        scale = effective_minimap_radius_pixels / detection_radius

        minimap_offset_x = relative_dx * scale
        minimap_offset_y = relative_dy * scale # 假设Y轴方向一致 (向下为正)

        center_x, center_y = self.minimap_rect.center
        return (int(center_x + minimap_offset_x), int(center_y + minimap_offset_y))

    def render_minimap(self):
        # 1. 绘制小地图背景
        minimap_surface = pygame.Surface(self.minimap_rect.size, pygame.SRCALPHA)
        minimap_surface.fill(self.minimap_bg_color)
        self.screen.blit(minimap_surface, self.minimap_rect.topleft)

        # 2. 绘制小地图边框
        pygame.draw.rect(self.screen, self.minimap_border_color, self.minimap_rect, 1)

        # 3. 绘制标题
        title_text = self.small_font.render("地图分布", True, (255, 255, 255))
        title_rect = title_text.get_rect(centerx=self.minimap_rect.centerx, y=self.minimap_rect.y - 15)
        self.screen.blit(title_text, title_rect)

        # 4. 获取地图数据 - 增强版错误诊断
        if not self.map_manager:
            # 显示地图管理器未设置提示
            no_data_text = self.small_font.render("地图管理器未设置", True, (255, 100, 100))
            no_data_rect = no_data_text.get_rect(center=self.minimap_rect.center)
            self.screen.blit(no_data_text, no_data_rect)
            return
        
        if not self.map_manager.current_map:
            # 显示当前地图未选择提示
            no_data_text = self.small_font.render("未选择地图", True, (255, 200, 100))
            no_data_rect = no_data_text.get_rect(center=self.minimap_rect.center)
            self.screen.blit(no_data_text, no_data_rect)
            return

        current_map = self.map_manager.current_map
        map_width = getattr(current_map, 'width', 50)
        map_height = getattr(current_map, 'height', 50)
        
        # 5. 计算缩放比例 - 将整个地图显示在小地图区域内
        scale_x = (self.minimap_rect.width - 10) / map_width  # 留5像素边距
        scale_y = (self.minimap_rect.height - 10) / map_height
        scale = min(scale_x, scale_y)  # 使用较小的缩放比例保持比例
        
        # 6. 计算地图在小地图中的显示区域
        map_display_width = int(map_width * scale)
        map_display_height = int(map_height * scale)
        map_start_x = self.minimap_rect.x + (self.minimap_rect.width - map_display_width) // 2
        map_start_y = self.minimap_rect.y + (self.minimap_rect.height - map_display_height) // 2

        # 7. 绘制地图网格（可选）
        grid_color = (60, 60, 60)
        for i in range(0, map_width + 1, 10):  # 每10格画一条网格线
            x = map_start_x + int(i * scale)
            if x <= map_start_x + map_display_width:
                pygame.draw.line(self.screen, grid_color, (x, map_start_y), (x, map_start_y + map_display_height))
        
        for j in range(0, map_height + 1, 10):
            y = map_start_y + int(j * scale)
            if y <= map_start_y + map_display_height:
                pygame.draw.line(self.screen, grid_color, (map_start_x, y), (map_start_x + map_display_width, y))

        # 8. 绘制玩家位置 - 修复版，使用真实的玩家位置数据
        player_x, player_y = 25, 25  # 默认玩家位置
        
        # 尝试从多个可能的位置获取玩家坐标
        if hasattr(self.battle_manager, 'player_instance') and self.battle_manager.player_instance:
            player_instance = self.battle_manager.player_instance
            
            # 检查position属性
            if hasattr(player_instance, 'position') and player_instance.position:
                player_x, player_y = player_instance.position
            # 检查单独的x, y属性
            elif hasattr(player_instance, 'x') and hasattr(player_instance, 'y'):
                player_x = player_instance.x
                player_y = player_instance.y
            # 检查战斗管理器中是否有移动后的位置
            elif hasattr(self.battle_manager, '_player_current_position'):
                player_x, player_y = self.battle_manager._player_current_position
        
        # 如果战斗管理器有实时位置更新，使用该位置
        if hasattr(self.battle_manager, '_current_player_pos'):
            player_x, player_y = self.battle_manager._current_player_pos
        
        player_screen_x = map_start_x + int(player_x * scale)
        player_screen_y = map_start_y + int(player_y * scale)
        
        # 绘制玩家位置（蓝色圆点，更显眼）
        pygame.draw.circle(self.screen, (0, 150, 255), (player_screen_x, player_screen_y), 4)
        pygame.draw.circle(self.screen, (255, 255, 255), (player_screen_x, player_screen_y), 2)

        # 9. 绘制所有怪物 - 展示分散分布效果
        if hasattr(current_map, 'active_enemies') and current_map.active_enemies:
            for enemy in current_map.active_enemies:
                if hasattr(enemy, 'position') and enemy.position:
                    enemy_x, enemy_y = enemy.position
                    
                    # 转换为小地图坐标
                    enemy_screen_x = map_start_x + int(enemy_x * scale)
                    enemy_screen_y = map_start_y + int(enemy_y * scale)
                    
                    # 确保怪物点在显示区域内
                    if (map_start_x <= enemy_screen_x <= map_start_x + map_display_width and 
                        map_start_y <= enemy_screen_y <= map_start_y + map_display_height):
                        
                        # 根据怪物类型选择颜色
                        monster_type = getattr(enemy, 'type', '普通')
                        if monster_type == "世界BOSS":
                            color = (255, 165, 0)  # 橙色
                            radius = 4
                        elif monster_type == "首领" or getattr(enemy, 'is_boss', False):
                            color = (255, 0, 0)    # 红色
                            radius = 3
                        elif monster_type == "精英":
                            color = (0, 255, 0)    # 绿色
                            radius = 2
                        else:
                            color = (200, 200, 200)  # 灰色 (普通)
                            radius = 2
                        
                        # 绘制怪物点
                        pygame.draw.circle(self.screen, color, (enemy_screen_x, enemy_screen_y), radius)

        # 10. 绘制寻路轨迹和目标 - 增强版可视化
        if hasattr(self.battle_manager, 'current_target_enemy') and self.battle_manager.current_target_enemy:
            target_enemy = self.battle_manager.current_target_enemy
            if isinstance(target_enemy, dict) and 'position' in target_enemy:
                target_pos = target_enemy['position']
            elif hasattr(target_enemy, 'position'):
                target_pos = target_enemy.position
            else:
                target_pos = None
                
            if target_pos:
                target_x, target_y = target_pos
                target_screen_x = map_start_x + int(target_x * scale)
                target_screen_y = map_start_y + int(target_y * scale)
                
                # 绘制目标标记（动态脉冲效果）
                if (map_start_x <= target_screen_x <= map_start_x + map_display_width and 
                    map_start_y <= target_screen_y <= map_start_y + map_display_height):
                    
                    # 计算动态脉冲半径
                    import time
                    pulse_time = time.time() * 3  # 脉冲速度
                    pulse_radius = 3 + int(2 * abs(math.sin(pulse_time)))
                    
                    # 绘制目标怪物标记（红色脉冲圆环）
                    pygame.draw.circle(self.screen, (255, 0, 0), (target_screen_x, target_screen_y), pulse_radius, 2)
                    pygame.draw.circle(self.screen, (255, 255, 0), (target_screen_x, target_screen_y), 3)
                    
                    # 绘制寻路轨迹线（虚线效果）
                    dx = target_screen_x - player_screen_x
                    dy = target_screen_y - player_screen_y
                    distance = (dx * dx + dy * dy) ** 0.5
                    
                    if distance > 0:
                        # 绘制虚线路径
                        steps = int(distance / 5)  # 每5像素一段
                        for i in range(0, steps, 2):  # 跳跃绘制形成虚线
                            start_ratio = i / steps
                            end_ratio = min((i + 1) / steps, 1.0)
                            
                            start_x = player_screen_x + int(dx * start_ratio)
                            start_y = player_screen_y + int(dy * start_ratio)
                            end_x = player_screen_x + int(dx * end_ratio)
                            end_y = player_screen_y + int(dy * end_ratio)
                            
                            pygame.draw.line(self.screen, (255, 255, 0), (start_x, start_y), (end_x, end_y), 2)
                    
                    # 绘制距离信息
                    real_distance = ((target_x - player_x) ** 2 + (target_y - player_y) ** 2) ** 0.5
                    distance_text = f"{real_distance:.1f}"
                    distance_surface = self.small_font.render(distance_text, True, (255, 255, 0))
                    
                    # 将距离文本显示在连线中点
                    mid_x = (player_screen_x + target_screen_x) // 2
                    mid_y = (player_screen_y + target_screen_y) // 2 - 10
                    
                    # 添加背景框增强可读性
                    text_rect = distance_surface.get_rect(center=(mid_x, mid_y))
                    pygame.draw.rect(self.screen, (0, 0, 0, 128), text_rect.inflate(4, 2))
                    self.screen.blit(distance_surface, text_rect)
        
        # 11. 绘制移动轨迹历史（如果有）
        if hasattr(self.battle_manager, '_movement_trail') and self.battle_manager._movement_trail:
            trail_points = []
            for trail_pos in self.battle_manager._movement_trail[-20:]:  # 最近20个位置点
                trail_x, trail_y = trail_pos
                trail_screen_x = map_start_x + int(trail_x * scale)
                trail_screen_y = map_start_y + int(trail_y * scale)
                
                if (map_start_x <= trail_screen_x <= map_start_x + map_display_width and 
                    map_start_y <= trail_screen_y <= map_start_y + map_display_height):
                    trail_points.append((trail_screen_x, trail_screen_y))
            
            # 绘制移动轨迹
            if len(trail_points) > 1:
                for i in range(len(trail_points) - 1):
                    alpha = int(255 * (i + 1) / len(trail_points))  # 轨迹渐变透明度
                    color = (0, 255, 255, alpha)  # 青色轨迹
                    pygame.draw.line(self.screen, color[:3], trail_points[i], trail_points[i + 1], 1)

        # 12. 绘制图例
        legend_y = self.minimap_rect.bottom + 5
        legend_items = [
            ("普通", (200, 200, 200), 2),
            ("精英", (0, 255, 0), 2),
            ("首领", (255, 0, 0), 3),
            ("BOSS", (255, 165, 0), 4)
        ]
        
        legend_x = self.minimap_rect.x
        for i, (name, color, radius) in enumerate(legend_items):
            if legend_y + 12 < self.screen.get_height():  # 确保图例在屏幕内
                pygame.draw.circle(self.screen, color, (legend_x + 8, legend_y + 6), radius)
                legend_text = self.small_font.render(name, True, (255, 255, 255))
                self.screen.blit(legend_text, (legend_x + 18, legend_y))
                legend_x += 50  # 水平排列图例项

    def render_player(self):
        if self.player_image:
            image_rect = self.player_image.get_rect(center=self.player_position)
            self.screen.blit(self.player_image, image_rect)
        else:
            pygame.draw.circle(self.screen, (0, 255, 0), self.player_position, 20)

        player_text = self.font.render("玩家", True, (255, 255, 255))
        text_rect = player_text.get_rect(center=(self.player_position[0], self.player_position[1] - 35))
        self.screen.blit(player_text, text_rect)

        hp_bar_width = 60
        hp_bar_height = 8
        hp_bar_x = self.player_position[0] - hp_bar_width // 2
        hp_bar_y = self.player_position[1] + 30

        pygame.draw.rect(self.screen, (100, 0, 0), (hp_bar_x, hp_bar_y, hp_bar_width, hp_bar_height))
        if self.player.max_hp > 0:
            # 使用current_hp来显示实时血量，如果没有current_hp则使用hp
            current_hp = getattr(self.player, 'current_hp', self.player.hp)
            hp_ratio = current_hp / self.player.max_hp
            current_hp_width = int(hp_bar_width * hp_ratio)
            pygame.draw.rect(self.screen, (255, 0, 0), (hp_bar_x, hp_bar_y, current_hp_width, hp_bar_height))

        mp_bar_y = hp_bar_y + hp_bar_height + 2
        pygame.draw.rect(self.screen, (0, 0, 100), (hp_bar_x, mp_bar_y, hp_bar_width, hp_bar_height))
        if self.player.max_mp > 0:
            # 使用current_mp来显示实时魔法值，如果没有current_mp则使用mp
            current_mp = getattr(self.player, 'current_mp', self.player.mp)
            mp_ratio = current_mp / self.player.max_mp
            current_mp_width = int(hp_bar_width * mp_ratio)
            pygame.draw.rect(self.screen, (0, 100, 255), (hp_bar_x, mp_bar_y, current_mp_width, hp_bar_height))

        self.render_action_progress_bar(self.player_position, is_player=True)

    def render_enemies(self):
        """渲染敌人 - 恢复使用原来的敌人位置定义"""
        enemies_to_render = []
        
        # 只显示实际战斗中的敌人
        if (hasattr(self.battle_manager, 'is_in_battle') and 
            self.battle_manager.is_in_battle and
            hasattr(self.battle_manager, 'current_enemy') and
            self.battle_manager.current_enemy):
            
            enemies_to_render = [self.battle_manager.current_enemy]
        
        for i, enemy in enumerate(enemies_to_render):
            # 🔧 恢复使用原来的敌人位置系统
            if i < len(self.enemy_positions):
                enemy_position = self.enemy_positions[i]
            else:
                # 如果超出预定义位置，使用备用位置
                enemy_position = (self.rect.width - 150, 150 + i * 120)
            
            # 渲染敌人图像
            enemy_name = getattr(enemy, 'name', 'Unknown')
            enemy_image = None
            
            # 尝试获取敌人图像
            if hasattr(enemy, 'get_image') and callable(enemy.get_image):
                try:
                    enemy_image = enemy.get_image((60, 60))
                except:
                    enemy_image = None
            
            # 如果没有图像，创建默认图像
            if enemy_image is None:
                enemy_image = self.create_default_enemy_image((60, 60), enemy_name)
            
            if enemy_image:
                enemy_rect = enemy_image.get_rect(center=enemy_position)
                self.screen.blit(enemy_image, enemy_rect)
            
            # 渲染敌人血条
            self.render_enemy_hp_bar(enemy, enemy_position)
            
            # 渲染敌人进度条（行动条）
            self.render_action_progress_bar(enemy_position, is_player=False, enemy_index=i)

    def render_battle_effects(self):
        for effect in self.battle_effects:
            if effect["type"] == "attack":
                pygame.draw.line(self.screen, (255, 255, 0), effect["start_pos"], effect["end_pos"], 3)
            elif effect["type"] == "skill":
                pygame.draw.circle(self.screen, (255, 0, 255), effect["position"], effect["radius"], 3)

    def render_action_progress_bar(self, position, is_player=True, enemy_index=0):
        """渲染行动进度条 - 只在真实战斗时显示"""
        # 🔧 只在真实战斗时显示进度条
        if not (hasattr(self.battle_manager, 'is_in_battle') and self.battle_manager.is_in_battle):
            return
        
        # 🔧 获取进度值 - 真实战斗模式
        progress = 0.0
        
        if hasattr(self.battle_manager, 'get_action_progress'):
            try:
                attacker_progress, defender_progress = self.battle_manager.get_action_progress()
                
                # 🔧 智能的进度分配逻辑
                if is_player:
                    # 如果玩家是攻击方，使用攻击方进度，否则使用防守方进度
                    if (hasattr(self.battle_manager, 'attacker') and 
                        self.battle_manager.attacker == self.player):
                        progress = attacker_progress
                    else:
                        progress = defender_progress
                else:
                    # 对于怪物，如果怪物是防守方，使用防守方进度，否则使用攻击方进度
                    if (hasattr(self.battle_manager, 'defender') and 
                        hasattr(self.battle_manager, 'current_enemy') and
                        self.battle_manager.defender == self.battle_manager.current_enemy):
                        progress = defender_progress
                    else:
                        progress = attacker_progress
                        
            except Exception as e:
                print(f"获取战斗进度时出错: {e}")
                return
        else:
            return

        # 🔧 计算进度条位置 - 使用和血量法力值进度条相同的宽度和对齐方式
        # 保持和血量法力值进度条一致的宽度（60像素）
        progress_bar_width = 60  # 和血量法力值进度条一致
        bar_x = position[0] - progress_bar_width // 2
        
        if is_player:
            hp_bar_y = position[1] + 30
            mp_bar_y = hp_bar_y + 8 + 2 # hp_bar_height 是 8
            bar_y = mp_bar_y + 8 + 5  # mp_bar_height 是 8, 再加5像素间距
        else:
            hp_bar_y = position[1] + 35  # 怪物血条位置
            bar_y = hp_bar_y + 8 + 5   # 怪物血条下方

        # 🔧 绘制进度条 - 改进版，添加更多视觉效果
        # 背景
        pygame.draw.rect(self.screen, self.progress_bar_bg_color, 
                        (bar_x, bar_y, progress_bar_width, self.progress_bar_height))
        
        # 边框
        pygame.draw.rect(self.screen, self.progress_bar_border_color, 
                        (bar_x, bar_y, progress_bar_width, self.progress_bar_height), 1)
        
        # 进度填充
        if progress > 0:
            fill_width = int(progress_bar_width * min(1.0, progress))
            if fill_width > 0:
                # 🔧 改进：根据进度使用渐变颜色
                if progress < 0.3:
                    fill_color = (255, 100, 100)  # 红色
                elif progress < 0.7:
                    fill_color = (255, 255, 100)  # 黄色
                else:
                    fill_color = (100, 255, 100)  # 绿色
                
                pygame.draw.rect(self.screen, fill_color, 
                               (bar_x, bar_y, fill_width, self.progress_bar_height))
                
                # 添加高光效果
                if fill_width > 2:
                    highlight_color = tuple(min(255, c + 50) for c in fill_color)
                    pygame.draw.rect(self.screen, highlight_color, 
                                   (bar_x, bar_y, fill_width, 2))

        # 进度文本 - 只显示在真实战斗时
        if is_player:
            progress_text = f"玩家: {int(progress * 100)}%"
        else:
            progress_text = f"怪物: {int(progress * 100)}%"
            
        text_surface = self.small_font.render(progress_text, True, (255, 255, 255))
        text_rect = text_surface.get_rect(center=(position[0], bar_y + self.progress_bar_height + 12))
        self.screen.blit(text_surface, text_rect)

        # 攻击速度信息
        if is_player:
            attack_speed = getattr(self.player, 'attack_speed', 1.0)
        else:
            attack_speed = 1.0
            # 尝试从当前战斗怪物获取攻击速度
            if (hasattr(self.battle_manager, 'current_enemy') and 
                self.battle_manager.current_enemy):
                attack_speed = getattr(self.battle_manager.current_enemy, 'attack_speed', 1.0)

        speed_text = f"速度: {attack_speed:.1f}"
        speed_surface = self.small_font.render(speed_text, True, (200, 200, 200))
        speed_rect = speed_surface.get_rect(center=(position[0], bar_y + self.progress_bar_height + 24))
        self.screen.blit(speed_surface, speed_rect)

    def render_damage_numbers(self):
        for damage_info in self.damage_numbers:
            x = damage_info["position"][0]
            y = damage_info["position"][1] - damage_info["offset"]
            color = (255, 255, 0) if damage_info["is_critical"] else (255, 255, 255)
            font_to_use = self.font if damage_info["is_critical"] else self.small_font # 根据是否暴击选择字体
            damage_text = font_to_use.render(str(damage_info["damage"]), True, color)
            text_rect = damage_text.get_rect(center=(x, y))
            self.screen.blit(damage_text, text_rect)

    # 移除手动攻击方法，因为现在攻击和技能都是自动释放的
    # use_basic_attack 和 use_skill 方法已被移除

    def add_auto_battle_button(self):
        button_width = 100
        button_height = 35
        button_x = self.rect.width - button_width - 10
        button_y = self.rect.height - button_height - 10
        button_text = self.battle_manager.get_auto_battle_status()
        
        # 根据不同状态设置按钮颜色
        if hasattr(self.battle_manager, 'player_is_dead') and self.battle_manager.player_is_dead:
            button_color = (100, 100, 100)  # 灰色，表示不可用
        elif self.battle_manager.is_auto_battle_active():
            button_color = (200, 50, 50)    # 红色，表示停止
        else:
            button_color = (50, 150, 50)    # 绿色，表示开始
            
        self.add_button(button_text, (button_x, button_y, button_width, button_height), self.toggle_auto_battle, button_color)
    
    def add_boss_refresh_button(self):
        """添加Boss状态按钮（打开可移动的Boss刷新状态窗口）"""
        button_width = 80
        button_height = 30
        # Boss状态按钮水平坐标与寻怪地图按钮一致
        left_width = int(self.screen.get_width() * 0.25)  # 地图面板宽度
        grid_margin_horizontal = 10  # 与地图面板的grid_margin_horizontal一致
        button_x = grid_margin_horizontal  # 与地图面板按钮的x坐标保持一致
        button_y = self.rect.top + 10
        
        self.add_button(
            "Boss状态",
            (button_x, button_y, button_width, button_height),
            self.toggle_boss_refresh_panel,
            (70, 130, 180),  # 蓝色背景，表示信息窗口
            (255, 255, 255)  # 白色文字
        )
    
    def add_auto_skill_button(self):
        """添加自动技能按钮（自动战斗按钮左边5像素）"""
        button_width = 80
        button_height = 35
        # 放置在自动战斗按钮左边5像素
        auto_battle_x = self.rect.width - 100 - 10  # 自动战斗按钮的x位置（宽度100）
        button_x = auto_battle_x - button_width - 5  # 在自动战斗按钮左边5像素
        button_y = self.rect.height - button_height - 10  # 与自动战斗按钮同一水平位置
        
        self.add_button(
            "自动技能", 
            (button_x, button_y, button_width, button_height),
            self.open_auto_skill_system,
            (255, 140, 0),  # 橙色背景
            (255, 255, 255)  # 白色文字
        )

    def toggle_auto_battle(self):
        # 检查玩家是否死亡，死亡时不允许启动自动战斗
        if hasattr(self.battle_manager, 'player_is_dead') and self.battle_manager.player_is_dead:
            print("玩家死亡期间无法启动自动战斗")
            return
            
        self.battle_manager.toggle_auto_battle()
        self.create_skill_buttons() # 重新创建按钮以更新文本和颜色
        print(f"自动战斗状态: {self.battle_manager.get_auto_battle_status()}")
    
    def toggle_boss_refresh_panel(self):
        """打开Boss刷新状态窗口（可移动版本）"""
        print("🏆 打开Boss刷新状态窗口...")
        try:
            from game.ui.boss_refresh_window import open_boss_refresh_window
            open_boss_refresh_window(self.map_manager)
            print("✅ Boss刷新状态窗口已打开")
        except Exception as e:
            print(f"❌ 打开Boss刷新状态窗口失败: {e}")
            import traceback
            traceback.print_exc()
    
    def open_auto_skill_system(self):
        """打开自动技能系统界面"""
        print("🎯 打开自动技能系统...")
        
        def run_auto_skill_system():
            """在单独线程中运行自动技能系统界面"""
            try:
                # 导入必要的模块
                import tkinter as tk
                from threading import Thread
                
                # 创建技能系统主窗口
                skill_window = tk.Tk()
                skill_window.title("自动技能系统")
                skill_window.geometry("800x600")
                skill_window.configure(bg="#2D2D2D")
                
                # 创建标签页框架
                tab_frame = tk.Frame(skill_window, bg="#2D2D2D")
                tab_frame.pack(fill=tk.X, padx=10, pady=5)
                
                # 标签页按钮
                tab_buttons_frame = tk.Frame(tab_frame, bg="#2D2D2D")
                tab_buttons_frame.pack()
                
                # 内容显示区域
                content_frame = tk.Frame(skill_window, bg="#2D2D2D")
                content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                
                # 创建两个标签页
                skill_config_frame = tk.Frame(content_frame, bg="#333333")
                skill_status_frame = tk.Frame(content_frame, bg="#333333")
                
                # 初始显示技能配置页面
                current_frame = skill_config_frame
                current_frame.pack(fill=tk.BOTH, expand=True)
                
                def show_skill_config():
                    nonlocal current_frame
                    current_frame.pack_forget()
                    current_frame = skill_config_frame
                    current_frame.pack(fill=tk.BOTH, expand=True)
                    config_btn.configure(bg="#4D4D4D")
                    status_btn.configure(bg="#3D3D3D")
                
                def show_skill_status():
                    nonlocal current_frame
                    current_frame.pack_forget()
                    current_frame = skill_status_frame
                    current_frame.pack(fill=tk.BOTH, expand=True)
                    status_btn.configure(bg="#4D4D4D")
                    config_btn.configure(bg="#3D3D3D")
                
                # 标签页按钮
                config_btn = tk.Button(
                    tab_buttons_frame,
                    text="技能配置",
                    bg="#4D4D4D",
                    fg="#FFFFFF",
                    font=("微软雅黑", 10, "bold"),
                    command=show_skill_config,
                    relief=tk.FLAT,
                    padx=20,
                    pady=5
                )
                config_btn.pack(side=tk.LEFT, padx=2)
                
                status_btn = tk.Button(
                    tab_buttons_frame,
                    text="状态监控",
                    bg="#3D3D3D",
                    fg="#FFFFFF",
                    font=("微软雅黑", 10, "bold"),
                    command=show_skill_status,
                    relief=tk.FLAT,
                    padx=20,
                    pady=5
                )
                status_btn.pack(side=tk.LEFT, padx=2)
                
                # 技能配置页面内容
                self._create_skill_config_page(skill_config_frame)
                
                # 状态监控页面内容
                self._create_skill_status_page(skill_status_frame)
                
                print("✅ 自动技能系统界面创建成功")
                
                # 启动Tkinter主循环
                skill_window.mainloop()
                print("🔚 自动技能系统界面已关闭")
                
            except Exception as e:
                print(f"❌ 打开自动技能系统失败: {e}")
                import traceback
                traceback.print_exc()
                
                # 备用方案：直接打开技能面板
                try:
                    print("🔧 尝试直接打开技能面板...")
                    from main import open_skill_panel
                    open_skill_panel(self.player)
                except Exception as backup_e:
                    print(f"❌ 备用方案也失败: {backup_e}")
        
        # 在单独线程中运行，避免阻塞游戏主界面
        from threading import Thread
        system_thread = Thread(target=run_auto_skill_system)
        system_thread.daemon = True
        system_thread.start()
    
    def _create_skill_config_page(self, parent_frame):
        """创建技能配置页面"""
        # 标题
        title_label = tk.Label(
            parent_frame,
            text="🎯 技能自动释放配置",
            bg="#333333",
            fg="#FFFFFF",
            font=("微软雅黑", 14, "bold")
        )
        title_label.pack(pady=10)
        
        # 提示信息
        info_label = tk.Label(
            parent_frame,
            text="在这里配置哪些技能需要自动释放\n技能配置会实时同步到自动释放系统",
            bg="#333333",
            fg="#CCCCCC",
            font=("微软雅黑", 10),
            justify=tk.CENTER
        )
        info_label.pack(pady=5)
        
        # 打开技能面板按钮
        open_panel_btn = tk.Button(
            parent_frame,
            text="打开技能面板进行配置",
            bg="#4CAF50",
            fg="#FFFFFF",
            font=("微软雅黑", 12, "bold"),
            command=self._open_skill_panel_for_config,
            relief=tk.RAISED,
            bd=2,
            padx=20,
            pady=10
        )
        open_panel_btn.pack(pady=20)
        
        # 使用说明
        instruction_text = """
使用说明：
1. 点击上方按钮打开技能面板
2. 在技能面板中，已学会的技能旁边有"自动释放"开关
3. 开启开关后，该技能会自动释放
4. 配置完成后可在"状态监控"页面查看效果
        """
        
        instruction_label = tk.Label(
            parent_frame,
            text=instruction_text,
            bg="#333333",
            fg="#AAAAAA",
            font=("微软雅黑", 9),
            justify=tk.LEFT,
            anchor=tk.W
        )
        instruction_label.pack(pady=20, padx=20, anchor=tk.W)
    
    def _create_skill_status_page(self, parent_frame):
        """创建技能状态监控页面"""
        # 标题
        title_label = tk.Label(
            parent_frame,
            text="📊 自动技能状态监控",
            bg="#333333",
            fg="#FFFFFF",
            font=("微软雅黑", 14, "bold")
        )
        title_label.pack(pady=10)
        
        # 提示信息
        info_label = tk.Label(
            parent_frame,
            text="监控自动技能的释放状态和冷却时间",
            bg="#333333",
            fg="#CCCCCC",
            font=("微软雅黑", 10)
        )
        info_label.pack(pady=5)
        
        # 打开自动技能栏按钮
        open_hotbar_btn = tk.Button(
            parent_frame,
            text="打开自动技能栏",
            bg="#FF9800",
            fg="#FFFFFF",
            font=("微软雅黑", 12, "bold"),
            command=self._open_skill_hotbar,
            relief=tk.RAISED,
            bd=2,
            padx=20,
            pady=10
        )
        open_hotbar_btn.pack(pady=20)
        
        # 状态信息显示区域
        status_frame = tk.Frame(parent_frame, bg="#2A2A2A", relief=tk.SUNKEN, bd=2)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        status_title = tk.Label(
            status_frame,
            text="当前启用的自动技能：",
            bg="#2A2A2A",
            fg="#FFFFFF",
            font=("微软雅黑", 11, "bold")
        )
        status_title.pack(pady=10)
        
        # 显示配置状态
        try:
            import json
            import os
            config_path = os.path.join("saves", "auto_cast_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                enabled_skills = [skill for skill, enabled in config.items() if enabled]
                
                if enabled_skills:
                    for skill in enabled_skills:
                        skill_label = tk.Label(
                            status_frame,
                            text=f"✅ {skill}",
                            bg="#2A2A2A",
                            fg="#00FF00",
                            font=("微软雅黑", 10)
                        )
                        skill_label.pack(anchor=tk.W, padx=20, pady=2)
                else:
                    no_skill_label = tk.Label(
                        status_frame,
                        text="❌ 暂无启用的自动技能",
                        bg="#2A2A2A",
                        fg="#FF6666",
                        font=("微软雅黑", 10)
                    )
                    no_skill_label.pack(pady=10)
            else:
                no_config_label = tk.Label(
                    status_frame,
                    text="⚠️ 未找到配置文件，请先进行技能配置",
                    bg="#2A2A2A",
                    fg="#FFAA00",
                    font=("微软雅黑", 10)
                )
                no_config_label.pack(pady=10)
                
        except Exception as e:
            error_label = tk.Label(
                status_frame,
                text=f"❌ 读取配置失败: {e}",
                bg="#2A2A2A",
                fg="#FF6666",
                font=("微软雅黑", 10)
            )
            error_label.pack(pady=10)
    
    def _open_skill_panel_for_config(self):
        """打开技能面板进行配置"""
        try:
            from main import open_skill_panel
            open_skill_panel(self.player)
            print("✅ 技能配置面板已打开")
        except Exception as e:
            print(f"❌ 打开技能配置面板失败: {e}")
    
    def _open_skill_hotbar(self):
        """打开自动技能栏"""
        try:
            from main import open_skill_hotbar
            open_skill_hotbar(self.player)
            print("✅ 自动技能栏已打开")
        except Exception as e:
            print(f"❌ 打开自动技能栏失败: {e}")
    
    def handle_event(self, event):
        """处理事件"""
        # Boss刷新状态窗口现在是独立窗口，不需要在此处理事件
        # 直接调用父类的事件处理（处理按钮点击等）
        return super().handle_event(event)

    def update_auto_battle_button(self):
        for button in self.buttons:
            # 依赖文本来识别按钮有些脆弱，最好使用按钮ID或类型
            if ("战斗" in button["text"] or "停止" in button["text"] or "寻路" in button["text"] or 
                "死亡" in button["text"]): # 增加了 "死亡"
                button["text"] = self.battle_manager.get_auto_battle_status()
                
                # 根据不同状态设置按钮颜色
                if hasattr(self.battle_manager, 'player_is_dead') and self.battle_manager.player_is_dead:
                    button["bg_color"] = (100, 100, 100)  # 灰色，表示不可用
                elif self.battle_manager.is_auto_battle_active():
                    button["bg_color"] = (200, 50, 50)    # 红色，表示停止
                else:
                    button["bg_color"] = (50, 150, 50)    # 绿色，表示开始
                break

    def render_revival_countdown(self, revival_info):
        """渲染复活倒计时界面"""
        try:
            # 计算中心位置
            center_x = self.rect.centerx
            center_y = self.rect.centery
            
            # 绘制半透明背景遮罩
            overlay = pygame.Surface((self.rect.width, self.rect.height), pygame.SRCALPHA)
            overlay.fill((0, 0, 0, 128))  # 半透明黑色
            self.screen.blit(overlay, self.rect.topleft)
            
            # 绘制死亡图标/骷髅头
            skull_radius = 40
            pygame.draw.circle(self.screen, (100, 100, 100), (center_x, center_y - 80), skull_radius)
            pygame.draw.circle(self.screen, (255, 255, 255), (center_x, center_y - 80), skull_radius, 3)
            
            # 绘制骷髅头眼睛
            eye_size = 8
            pygame.draw.circle(self.screen, (255, 0, 0), (center_x - 15, center_y - 85), eye_size)
            pygame.draw.circle(self.screen, (255, 0, 0), (center_x + 15, center_y - 85), eye_size)
            
            # 绘制"您已死亡"文本
            death_text = self.font.render("💀 您已死亡 💀", True, (255, 50, 50))
            death_rect = death_text.get_rect(center=(center_x, center_y - 20))
            self.screen.blit(death_text, death_rect)
            
            # 计算并显示剩余复活时间
            remaining_time = revival_info.get('remaining_time', 0)
            total_time = revival_info.get('total_time', 10)
            
            if remaining_time > 0:
                # 显示倒计时文本
                countdown_text = f"复活倒计时: {remaining_time:.1f} 秒"
                countdown_surface = self.font.render(countdown_text, True, (255, 255, 100))
                countdown_rect = countdown_surface.get_rect(center=(center_x, center_y + 20))
                self.screen.blit(countdown_surface, countdown_rect)
                
                # 绘制复活进度条
                progress_bar_width = 300
                progress_bar_height = 20
                progress_bar_x = center_x - progress_bar_width // 2
                progress_bar_y = center_y + 50
                
                # 进度条背景
                pygame.draw.rect(self.screen, (50, 50, 50), 
                               (progress_bar_x, progress_bar_y, progress_bar_width, progress_bar_height))
                pygame.draw.rect(self.screen, (255, 255, 255), 
                               (progress_bar_x, progress_bar_y, progress_bar_width, progress_bar_height), 2)
                
                # 进度条填充
                progress_ratio = 1.0 - (remaining_time / total_time)  # 进度从0到1
                progress_width = int(progress_bar_width * progress_ratio)
                if progress_width > 0:
                    # 渐变颜色：从红色到绿色
                    red_value = int(255 * (1 - progress_ratio))
                    green_value = int(255 * progress_ratio)
                    progress_color = (red_value, green_value, 50)
                    
                    pygame.draw.rect(self.screen, progress_color,
                                   (progress_bar_x, progress_bar_y, progress_width, progress_bar_height))
                
                # 进度百分比文本
                progress_percent = int(progress_ratio * 100)
                percent_text = f"{progress_percent}%"
                percent_surface = self.small_font.render(percent_text, True, (255, 255, 255))
                percent_rect = percent_surface.get_rect(center=(center_x, progress_bar_y + progress_bar_height // 2))
                self.screen.blit(percent_surface, percent_rect)
                
            else:
                # 复活完成
                revive_text = "正在复活..."
                revive_surface = self.font.render(revive_text, True, (50, 255, 50))
                revive_rect = revive_surface.get_rect(center=(center_x, center_y + 20))
                self.screen.blit(revive_surface, revive_rect)
            
            # 提示文本
            tip_text = "死亡期间无法进行任何操作"
            tip_surface = self.small_font.render(tip_text, True, (200, 200, 200))
            tip_rect = tip_surface.get_rect(center=(center_x, center_y + 100))
            self.screen.blit(tip_surface, tip_rect)
            
        except Exception as e:
            print(f"渲染复活倒计时时出错: {e}")
            # 出错时显示简单的死亡提示
            death_text = "玩家已死亡，等待复活..."
            death_surface = self.font.render(death_text, True, (255, 0, 0))
            death_rect = death_surface.get_rect(center=(self.rect.centerx, self.rect.centery))
            self.screen.blit(death_surface, death_rect)
    
    def toggle_auto_potion(self):
        """切换自动吃药状态"""
        self.auto_potion_enabled = not self.auto_potion_enabled
        status = "启用" if self.auto_potion_enabled else "禁用"
        print(f"自动吃药已{status}")
        return self.auto_potion_enabled
    
    def check_auto_potion(self, current_time):
        """检查并执行自动吃药逻辑 - 改进版，添加详细调试信息"""
        try:
            # 🔧 改进：添加详细的调试信息
            # print(f"🔍 检查自动药水 - 启用状态: {self.auto_potion_enabled}")
            
            # 检查自动药水是否启用
            if not self.auto_potion_enabled:
                return
            
            # 检查冷却时间
            if current_time - self.last_potion_use_time < self.potion_cooldown:
                # print(f"🕐 药水冷却中 - 剩余: {self.potion_cooldown - (current_time - self.last_potion_use_time)}ms")
                return
            
            # 检查玩家是否存在且未死亡
            if not self.player:
                print(f"❌ 玩家对象不存在")
                return
            
            # 检查玩家是否死亡
            if hasattr(self.battle_manager, 'player_is_dead') and self.battle_manager.player_is_dead:
                # 防止死亡信息刷屏：只在第一次或间隔足够长时显示
                if self.last_death_message_time == 0 or current_time - self.last_death_message_time > self.death_message_cooldown:
                    print(f"💀 玩家已死亡，跳过自动药水")
                    self.last_death_message_time = current_time
                return
            
            # 获取玩家当前HP和MP - 统一使用current_hp属性
            current_hp = getattr(self.player, 'current_hp', getattr(self.player, 'hp', 0))
            max_hp = getattr(self.player, 'max_hp', 100)
            current_mp = getattr(self.player, 'current_mp', getattr(self.player, 'mp', 0))
            max_mp = getattr(self.player, 'max_mp', 50)
            
            # 计算HP和MP百分比
            hp_percentage = (current_hp / max_hp * 100) if max_hp > 0 else 100
            mp_percentage = (current_mp / max_mp * 100) if max_mp > 0 else 100
            
            # 🔧 新增：显示玩家金币信息
            current_gold = getattr(self.player, 'currencies', {}).get('gold', 0) if hasattr(self.player, 'currencies') else 0
            
            # 检查是否需要购买并使用HP药水
            if hp_percentage < self.hp_threshold:
                print(f"🚨 HP低于阈值({self.hp_threshold}%) - 当前: {hp_percentage:.1f}%，尝试购买HP药水")
                if self.use_hp_potion():
                    self.last_potion_use_time = current_time
                    # 重新获取HP状态显示效果
                    new_hp = getattr(self.player, 'current_hp', getattr(self.player, 'hp', 0))
                    new_hp_percentage = (new_hp / max_hp * 100) if max_hp > 0 else 100
                    print(f"✅ 自动购买HP药水成功 - HP: {hp_percentage:.1f}% → {new_hp_percentage:.1f}%")
                    return
                else:
                    print(f"❌ 自动购买HP药水失败 (金币: {current_gold})")
            
            # 检查是否需要购买并使用MP药水
            if mp_percentage < self.mp_threshold:
                print(f"🚨 MP低于阈值({self.mp_threshold}%) - 当前: {mp_percentage:.1f}%，尝试购买MP药水")
                if self.use_mp_potion():
                    self.last_potion_use_time = current_time
                    # 重新获取MP状态显示效果
                    new_mp = getattr(self.player, 'current_mp', getattr(self.player, 'mp', 0))
                    new_mp_percentage = (new_mp / max_mp * 100) if max_mp > 0 else 100
                    print(f"✅ 自动购买MP药水成功 - MP: {mp_percentage:.1f}% → {new_mp_percentage:.1f}%")
                    return
                else:
                    print(f"❌ 自动购买MP药水失败 (金币: {current_gold})")
                    
        except Exception as e:
            print(f"❌ 自动吃药检查时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def use_hp_potion(self):
        """使用HP药水 - 直接购买并使用"""
        try:
            # 获取药水面板
            medicine_panel = getattr(self, 'medicine_panel', None)
            if not medicine_panel:
                return False
            
            # 查找最佳的启用HP药水
            best_hp_potion = self._find_best_enabled_potion('hp', medicine_panel)
            if not best_hp_potion:
                return False
            
            # 直接购买并使用药水
            if medicine_panel._buy_and_use_potion(best_hp_potion):
                print(f"💊 购买HP药水: {best_hp_potion.name} (-{best_hp_potion.price}金币)")
                return True
            else:
                print(f"❌ 购买失败: {best_hp_potion.name}")
                return False
            
        except Exception as e:
            print(f"❌ 自动使用HP药水时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def use_mp_potion(self):
        """使用MP药水 - 直接购买并使用"""
        try:
            # 获取药水面板
            medicine_panel = getattr(self, 'medicine_panel', None)
            if not medicine_panel:
                return False
            
            # 查找最佳的启用MP药水
            best_mp_potion = self._find_best_enabled_potion('mp', medicine_panel)
            if not best_mp_potion:
                return False
            
            # 直接购买并使用药水
            if medicine_panel._buy_and_use_potion(best_mp_potion):
                print(f"💊 购买MP药水: {best_mp_potion.name} (-{best_mp_potion.price}金币)")
                return True
            else:
                print(f"❌ 购买失败: {best_mp_potion.name}")
                return False
            
        except Exception as e:
            print(f"❌ 自动使用MP药水时出错: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _find_best_enabled_potion(self, potion_type, medicine_panel):
        """查找玩家选择的启用药水（按顺序选择第一个可用的）"""
        try:
            potion_config = getattr(medicine_panel, 'potion_config', None)
            if not potion_config:
                return None
            
            enabled_potions = potion_config.enabled_potions
            if not enabled_potions:
                return None
            
            # 映射药水类型常量
            from game.ui.medicine_panel import HP_POTION, MP_POTION, SPECIAL_POTION
            
            type_mapping = {
                'hp': HP_POTION,
                'mp': MP_POTION,
                'special': SPECIAL_POTION
            }
            
            actual_type = type_mapping.get(potion_type, potion_type)
            
            # 获取对应类型的药水
            potions = medicine_panel.potions.get(actual_type, [])
            
            # 按照药水在面板中的顺序，选择第一个启用且可购买的药水
            for potion in potions:
                if potion.id in enabled_potions and potion.enabled:
                    # 检查玩家是否有足够金币购买
                    if medicine_panel._check_player_gold(potion.price):
                        return potion
            return None
            
        except Exception as e:
            print(f"❌ 查找启用药水时出错: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def is_potion_type(self, item_name, potion_type):
        """检查物品是否是指定类型的药水"""
        try:
            # 加载物品配置
            import json
            import os
            
            config_path = "game/data/items_config.json"
            if not os.path.exists(config_path):
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                items_config = json.load(f)
            
            item_config = items_config.get(item_name, {})
            if item_config.get('category') != 'potion':
                return False
            
            effects = item_config.get('effects', {})
            
            if potion_type == 'hp':
                return effects.get('hp_restore', 0) > 0
            elif potion_type == 'mp':
                return effects.get('mp_restore', 0) > 0
            
            return False
            
        except Exception as e:
            print(f"检查药水类型时出错: {e}")
            return False
    
    def use_potion(self, potion_name, potion_type):
        """手动使用药水 - 新版：使用药水效果管理器"""
        try:
            inventory_manager = getattr(self.player, 'inventory_manager', None)
            if not inventory_manager:
                return False
            
            # 检查背包中是否有该药水
            if not hasattr(inventory_manager, 'has_item') or not inventory_manager.has_item(potion_name):
                return False
            
            # 获取药水效果配置
            import json
            import os
            
            config_path = "game/data/items_config.json"
            if not os.path.exists(config_path):
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                items_config = json.load(f)
            
            item_config = items_config.get(potion_name, {})
            effects = item_config.get('effects', {})
            
            # 使用新的药水效果管理器
            if hasattr(self, 'potion_effects_manager') and self.potion_effects_manager:
                # 构建药水效果配置
                effects_config = {
                    'hp_restore': effects.get('hp_restore', 0),
                    'mp_restore': effects.get('mp_restore', 0),
                    'recovery_type': effects.get('recovery_type', 'instant'),
                    'cooldown': effects.get('cooldown', 3000)
                }
                
                # 使用药水效果管理器应用效果
                success = self.potion_effects_manager.use_potion(potion_name, effects_config)
                
                if success:
                    # 从背包中移除药水
                    if hasattr(inventory_manager, 'remove_item'):
                        inventory_manager.remove_item(potion_name, 1)
                
                return success
            else:
                # 回退到传统方式
                print("⚠️ 使用传统药水效果应用方式")
                return self._use_potion_legacy(potion_name, effects, inventory_manager)
            
        except Exception as e:
            print(f"使用药水时出错: {e}")
            return False
    
    def _use_potion_legacy(self, potion_name, effects, inventory_manager):
        """传统药水使用方式（兼容性）"""
        try:
            # 应用药水效果
            hp_restore = effects.get('hp_restore', 0)
            mp_restore = effects.get('mp_restore', 0)
            
            if hp_restore > 0:
                self.restore_player_hp(hp_restore)
            
            if mp_restore > 0:
                self.restore_player_mp(mp_restore)
            
            # 从背包中移除药水
            if hasattr(inventory_manager, 'remove_item'):
                inventory_manager.remove_item(potion_name, 1)
            
            return True
            
        except Exception as e:
            print(f"传统药水使用时出错: {e}")
            return False
    
    def restore_player_hp(self, amount):
        """恢复玩家HP"""
        try:
            if hasattr(self.player, 'current_hp') and hasattr(self.player, 'max_hp'):
                self.player.current_hp = min(self.player.current_hp + amount, self.player.max_hp)
            elif hasattr(self.player, 'hp'):
                # 如果没有current_hp，直接使用hp属性
                max_hp = getattr(self.player, 'max_hp', self.player.hp)
                self.player.hp = min(self.player.hp + amount, max_hp)
            
            print(f"恢复HP: +{amount}")
            
        except Exception as e:
            print(f"恢复HP时出错: {e}")
    
    def restore_player_mp(self, amount):
        """恢复玩家MP"""
        try:
            if hasattr(self.player, 'current_mp') and hasattr(self.player, 'max_mp'):
                self.player.current_mp = min(self.player.current_mp + amount, self.player.max_mp)
            elif hasattr(self.player, 'mp'):
                # 如果没有current_mp，直接使用mp属性
                max_mp = getattr(self.player, 'max_mp', self.player.mp)
                self.player.mp = min(self.player.mp + amount, max_mp)
            
            print(f"恢复MP: +{amount}")
            
        except Exception as e:
            print(f"恢复MP时出错: {e}")

    def render_enemy_hp_bar(self, enemy, position):
        """渲染敌人血条"""
        # 显示怪物名称
        enemy_name = getattr(enemy, 'name', 'Unknown')
        enemy_text = self.font.render(enemy_name, True, (255, 255, 255))
        text_rect = enemy_text.get_rect(center=(position[0], position[1] - 45))
        self.screen.blit(enemy_text, text_rect)

        # 显示血条
        hp_bar_width = 60
        hp_bar_height = 8
        hp_bar_x = position[0] - hp_bar_width // 2
        hp_bar_y = position[1] + 35
        
        # 背景血条
        pygame.draw.rect(self.screen, (100, 0, 0), (hp_bar_x, hp_bar_y, hp_bar_width, hp_bar_height))
        
        # 当前血量条
        if hasattr(enemy, 'max_hp') and enemy.max_hp > 0:
            # 检查current_hp还是hp属性
            current_hp = getattr(enemy, 'current_hp', getattr(enemy, 'hp', 0))
            hp_ratio = current_hp / enemy.max_hp
            current_hp_width = int(hp_bar_width * hp_ratio)
            pygame.draw.rect(self.screen, (255, 0, 0), (hp_bar_x, hp_bar_y, current_hp_width, hp_bar_height))
        
        # 显示HP数值
        if hasattr(enemy, 'current_hp') and hasattr(enemy, 'max_hp'):
            hp_text = f"{enemy.current_hp}/{enemy.max_hp}"
        elif hasattr(enemy, 'hp') and hasattr(enemy, 'max_hp'):
            hp_text = f"{enemy.hp}/{enemy.max_hp}"
        else:
            hp_text = "HP: ?"
        
        hp_surface = self.small_font.render(hp_text, True, (255, 255, 255))
        hp_text_rect = hp_surface.get_rect(center=(position[0], hp_bar_y + hp_bar_height + 12))
        self.screen.blit(hp_surface, hp_text_rect)
    
    def render_battle_countdown(self):
        """在战斗界面最下方中间显示战斗倒计时"""
        if not self.battle_manager.is_in_battle:
            return
        
        # 获取战斗剩余时间
        remaining_time = self.battle_manager.get_battle_time_remaining()
        if remaining_time is None:
            return
        
        # 格式化时间显示
        minutes = int(remaining_time // 60)
        seconds = int(remaining_time % 60)
        time_text = f"战斗时间: {minutes:02d}:{seconds:02d}"
        
        # 根据剩余时间设置颜色
        if remaining_time <= 10:
            # 最后10秒红色警告
            color = (255, 0, 0)
        elif remaining_time <= 30:
            # 最后30秒橙色提醒
            color = (255, 165, 0)
        else:
            # 正常时间白色
            color = (255, 255, 255)
        
        # 渲染倒计时文本
        countdown_surface = self.font.render(time_text, True, color)
        
        # 计算位置：战斗界面最下方中间
        countdown_rect = countdown_surface.get_rect()
        countdown_rect.centerx = self.rect.centerx
        countdown_rect.bottom = self.rect.bottom - 10  # 距离底部10像素
        
        # 绘制半透明背景
        bg_rect = countdown_rect.inflate(20, 10)  # 背景稍大一些
        bg_surface = pygame.Surface((bg_rect.width, bg_rect.height))
        bg_surface.set_alpha(128)  # 半透明
        bg_surface.fill((0, 0, 0))  # 黑色背景
        self.screen.blit(bg_surface, bg_rect)
        
        # 绘制倒计时文本
        self.screen.blit(countdown_surface, countdown_rect)

    # 战斗控制按钮（移动到右侧，避免与小地图重叠）
    def add_quick_potion_setup(self):
        button_width = 100
        button_height = 30
        button_spacing = 35
        button_start_x = self.rect.width - button_width - 180  # 避开小地图区域
        button_start_y = self.rect.height - 200
        
        # 自动战斗按钮
        self.add_button("药水设置", (button_start_x, button_start_y, button_width, button_height), 
                        self.quick_potion_setup, bg_color=(60, 60, 120))

    def quick_potion_setup(self):
        """快速药水设置 - 智能一键启用药水系统"""
        try:
            print("🔧 启动智能药水配置...")
            
            # 🔧 改进：直接在战斗面板处理配置，无需依赖药水面板
            if not self.auto_potion_enabled:
                # 启用自动药水
                self.auto_potion_enabled = True
                
                # 设置合理的默认阈值
                self.hp_threshold = 50
                self.mp_threshold = 30
                
                # 保存配置
                self._save_potion_config_to_file()
                
                print(f"✅ 自动药水系统已启用")
                print(f"📊 HP阈值: {self.hp_threshold}% (HP低于此值时自动购买使用药水)")
                print(f"📊 MP阈值: {self.mp_threshold}% (MP低于此值时自动购买使用药水)")
                print(f"💰 将自动购买并使用 {self.hp_potions[0] if self.hp_potions else '未找到HP药水'}")
            else:
                # 已经启用，切换为禁用
                self.auto_potion_enabled = False
                self._save_potion_config_to_file()
                print(f"❌ 自动药水系统已禁用")
            
            # 🔧 新增：提供更好的用户反馈
            self._show_potion_status_message()
            
        except Exception as e:
            print(f"❌ 快速药水设置失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _show_potion_status_message(self):
        """
        显示药水系统状态信息
        为用户提供清晰的状态反馈
        """
        try:
            if self.auto_potion_enabled:
                print("🎯 自动药水系统状态:")
                print(f"   ⚡ 系统状态: 已启用")
                print(f"   💖 HP触发阈值: {self.hp_threshold}%")
                print(f"   💙 MP触发阈值: {self.mp_threshold}%")
                
                # 显示可用药水
                if self.hp_potions:
                    print(f"   🩸 HP药水: {self.hp_potions[0]} (优先使用)")
                if self.mp_potions:
                    print(f"   💙 MP药水: {self.mp_potions[0]} (优先使用)")
                    
                print("   💡 提示: 当HP/MP低于阈值时将自动购买并使用药水")
            else:
                print("❌ 自动药水系统已禁用")
                print("   💡 提示: 点击'药水设置'按钮快速启用")
                
        except Exception as e:
            print(f"显示药水状态时出错: {e}")